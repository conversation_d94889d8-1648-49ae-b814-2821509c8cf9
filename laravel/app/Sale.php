<?php

namespace App;

use App\Exceptions\CrmException;
use App\Models\Distributors;
use App\Models\MappingSale;
use App\Services\Enums\Ceiling;
use App\Traits\ModelAvailability;
use App\Traits\ModelImportable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use <PERSON>audenmeir\EloquentHasManyDeep\HasRelationships;
use \Znck\Eloquent\Traits\BelongsToThrough;

class Sale extends Model
{
    use BelongsToThrough;
    use ModelImportable;
    use ModelAvailability;
    use HasRelationships;

    protected $guard_name = 'api';

    protected $table = 'sales';

    protected $fillable = ['mapping_id','sale_ids', 'distributor_id', 'quantity', 'bonus', 'region', 'product_id', 'value', 'date', 'file_id', 'ceiling'];

    public function mappings()
    {
        //        Sale::where
        return $this->belongsToMany(Mapping::class)->using(MappingSale::class);
    }

    public function mappingDetails()
    {
        return $this->hasManyDeepFromRelations($this->mappings(), (new Mapping())->details());
    }

    public function mappingType()
    {
        return $this->hasOneDeepFromRelations($this->mappings(), (new Mapping())->type());
    }

    public function mappingSale(): HasMany
    {
        return $this->hasMany(MappingSale::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function details()
    {
        return $this->hasMany(SaleDetail::class);
    }

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }


    public function mappingDistributor()
    {
        return $this->belongsToThrough(Distributor::class, Mapping::class);
    }

    public function distributor(): BelongsTo
    {
        return $this->belongsTo(Distributor::class);
    }

    public function price($from = null, $to = null)
    {
        $value = Productprice::price($this->product, $this->distributor, $from, $to) ?: $this->value;
        return $value * $this->quantity;
    }

    /**
     * Scope to filter sales that have been distributed.
     * This includes sales with DISTRIBUTED ceiling status or sales that have child sales created from distribution.
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeDistributed(Builder $query): Builder
    {
        return $query->where('ceiling', Ceiling::DISTRIBUTED->value);
    }

    /**
     * Scope to filter sales that are in normal state.
     * These are sales that are not distributed and not limited by ceiling rules (BELOW status).
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeNormal(Builder $query): Builder
    {
        return $query->where('ceiling', Ceiling::BELOW->value);
    }


    /**
     * Scope to filter sales that are in normal or distributed state.
     * These are sales that are that considered to be the total after ceiling.
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeAfterCeiling(Builder $query) : Builder
    {
        return $query->whereIn('ceiling', [Ceiling::BELOW->value,Ceiling::DISTRIBUTED->value]);
    }

    /**
     * Scope to filter sales that are original sales.
     * These are sales that represent the original sales before any distribution or ceiling processing.
     * Original sales are identified by having a ceiling status of ABOVE (which indicates they were processed
     * and marked as original during distribution) or by being sales that don't reference other sales
     * in their sale_ids field (meaning they are the source sales).
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeOriginal(Builder $query): Builder
    {
        return $query->where(function ($query) {
            // Sales marked as ABOVE are original sales that were processed through ceiling distribution
            $query->where('ceiling', Ceiling::ABOVE->value)
                  // OR sales that don't have sale_ids (they are the original source)
                  ->orWhereNull('sale_ids')
                  // OR sales where sale_ids contains their own ID (they reference themselves as original)
                  ->orWhereRaw('FIND_IN_SET(id, sale_ids) > 0');
        });
    }
}
