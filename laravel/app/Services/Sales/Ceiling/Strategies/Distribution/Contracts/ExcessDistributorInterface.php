<?php

namespace App\Services\Sales\Ceiling\Strategies\Distribution\Contracts;

use App\Sale;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionType;

interface ExcessDistributorInterface
{
    /**
     * Distribute excess sale according to strategy-specific logic
     *
     * @param Sale $sale
     * @param array $salesContributionBaseOn
     * @param mixed $originalSale
     * @param DistributionType|null $distributionType
     * @return bool
     */
    public function distributeExcessSale(Sale $sale, array $salesContributionBaseOn,?Sale $originalSale = null, ?DistributionType $distributionType = null): bool;

    /**
     * Calculate excess quantity for distribution
     *
     * @param mixed $ceilingSale
     * @return float
     */
    public function calculateExcessQuantity($ceilingSale): float;
}
