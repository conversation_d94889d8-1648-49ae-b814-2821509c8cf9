<?php

namespace App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms;

use App\LineDivision;
use App\Sale;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\ExcessDistributorInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\SalesServiceFactoryInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionType;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleDetailFactory;
use App\Services\Enums\SaleDistribution;
use App\Services\SalesService;
use App\Services\Structure\LineDivisionService;
use Illuminate\Support\Collection;

/**
 * Hierarchical Chain Distribution Algorithm for Local Chain Pharmacies
 *
 * This algorithm implements a sophisticated distribution strategy specifically designed
 * for local pharmacy chains. It uses a hierarchical 100-distribution for current district approach
 * that reflects the typical organizational structure of local pharmacy chains.
 *
 */
class HierarchicalChainDistributionAlgorithm implements ExcessDistributorInterface
{

    public function __construct(
        private readonly SaleDetailFactory $saleDetailFactory,
        private readonly HierarchicalDistributionService $hierarchicalDistributionService,
        private readonly SalesServiceFactoryInterface $salesServiceFactory,
    )
    {

    }

    /**
     * Distribute excess sale using simple 100% distribution
     *
     * @param Sale $sale
     * @param array $salesContributionBaseOn
     * @param mixed $originalSale
     * @param DistributionType|null $distributionType
     * @return bool
     */
    public function distributeExcessSale(Sale $sale, array $salesContributionBaseOn, ?Sale $originalSale = null, ?DistributionType $distributionType = null): bool
    {
        $divisionsBricks = $this->getDistributionRatios($sale, $originalSale, $salesContributionBaseOn, $distributionType);

        $details = $this->saleDetailFactory->createDetailsFromRatios($sale, $divisionsBricks->toArray());

        return $this->saleDetailFactory->insertDetails($details);
    }

    /**
     * Calculate excess quantity for distribution
     * Updated to handle null limits from LEFT JOIN (STORES distribution type)
     *
     * @param mixed $ceilingSale
     * @return float
     */
    public function calculateExcessQuantity($ceilingSale): float
    {
        $limit = 0;
        if ($ceilingSale->number_of_units > 0) {
            $limit = $ceilingSale->limit ?? 0;
        }

        if ($ceilingSale->number_of_units < 0) {
            $limit = $ceilingSale->negative_limit ?? 0;
        }

        return $ceilingSale->number_of_units - $limit;
    }


    /**
     * Get secondary distribution ratios (10% portion)
     *
     * @param Sale $sale
     * @param Sale|null $originalSale
     * @param array $salesContributionBaseOn
     * @param DistributionType|null $distributionType
     * @return Collection
     */
    private function getDistributionRatios(Sale $sale,?Sale $originalSale, array $salesContributionBaseOn, ?DistributionType $distributionType = null): Collection
    {
        if (!$originalSale) {
            return collect();
        }

        $divisionIds = $this->hierarchicalDistributionService->getDeepestDescendantIds($originalSale);

        // Create SalesService with the appropriate DistributionType using factory
        $salesService = $this->salesServiceFactory->createForDistribution(SaleDistribution::NORMAL, $distributionType);

        return $salesService
            ->getRatiosForDistribution(
                $sale->date,
                $sale->product_id,
                $salesContributionBaseOn,
                $divisionIds
            );
    }

}
