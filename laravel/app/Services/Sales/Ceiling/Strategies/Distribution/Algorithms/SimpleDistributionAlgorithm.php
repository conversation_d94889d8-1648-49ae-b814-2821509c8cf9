<?php

namespace App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms;

use App\Sale;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\ExcessDistributorInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\SalesServiceFactoryInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionType;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleDetailFactory;
use App\Services\Enums\SaleDistribution;
use App\Services\SalesService;
use Illuminate\Support\Facades\Log;

/**
 * Simple distribution algorithm that distributes 100% of excess sales
 * using normal distribution ratios (used by PrivatePharmacyStrategy)
 */
class SimpleDistributionAlgorithm implements ExcessDistributorInterface
{
    private SaleDetailFactory $saleDetailFactory;
    private SalesServiceFactoryInterface $salesServiceFactory;

    public function __construct(
        SaleDetailFactory $saleDetailFactory,
        SalesServiceFactoryInterface $salesServiceFactory
    ) {
        $this->saleDetailFactory = $saleDetailFactory;
        $this->salesServiceFactory = $salesServiceFactory;
    }

    /**
     * Distribute excess sale using simple 100% distribution
     *
     * @param Sale $sale
     * @param array $salesContributionBaseOn
     * @param mixed $originalSale
     * @param DistributionType|null $distributionType
     * @return bool
     */
    public function distributeExcessSale(Sale $sale, array $salesContributionBaseOn,?Sale $originalSale = null, ?DistributionType $distributionType = null): bool
    {
        // Create SalesService with the appropriate DistributionType using factory
        $salesService = $this->salesServiceFactory->createForDistribution(SaleDistribution::NORMAL, $distributionType);


        $divisionsBricks = $salesService
            ->getRatiosForDistribution(
                $sale->date,
                $sale->product_id,
                $salesContributionBaseOn
            );

        $details = $this->saleDetailFactory->createDetailsFromRatios($sale, $divisionsBricks->toArray());

        // Validate distribution integrity before attempting insertion
        if (empty($details)) {
            Log::error('SimpleDistributionAlgorithm: Distribution failed - no details created', [
                'sale_id' => $sale->id,
                'ratios_count' => $divisionsBricks->count(),
                'reason' => 'No distribution ratios resulted in sale details'
            ]);
            return false;
        }

        $totalQuantity = array_sum(array_column($details, 'quantity'));
        $quantityMatch = abs($totalQuantity - $sale->quantity) < 0.01;

        if (!$quantityMatch) {
            Log::error('SimpleDistributionAlgorithm: Distribution failed - quantity mismatch', [
                'sale_id' => $sale->id,
                'expected_quantity' => $sale->quantity,
                'calculated_quantity' => $totalQuantity,
                'difference' => abs($totalQuantity - $sale->quantity)
            ]);
            return false;
        }

        return $this->saleDetailFactory->insertDetails($details);
    }

    /**
     * Calculate excess quantity for distribution
     * Updated to handle null limits from LEFT JOIN (STORES distribution type)
     *
     * @param mixed $ceilingSale
     * @return float
     */
    public function calculateExcessQuantity($ceilingSale): float
    {
        $limit = 0;
        if ($ceilingSale->number_of_units > 0) {
            $limit = $ceilingSale->limit ?? 0;
        }

        if ($ceilingSale->number_of_units < 0) {
            $limit = $ceilingSale->negative_limit ?? 0;
        }

        return $ceilingSale->number_of_units - $limit;
    }
}
