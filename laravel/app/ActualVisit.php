<?php

namespace App;

use App\Casts\CustomDate;
use App\Exceptions\CrmException;
use App\Models\ActualDoubleFeedback;
use App\Models\ActualVisitedDoctor;
use App\Models\Attachment;
use App\Models\DoubleVisitLocation;
use App\Models\EDetailing\Statistic;
use App\Traits\ModelAvailability;
use App\Traits\ModelExportable;
use App\Traits\ModelImportable;
use App\Traits\PdfExportable;
use App\Traits\SendMail;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Znck\Eloquent\Relations\BelongsToThrough;
use Znck\Eloquent\Traits\BelongsToThrough as TraitsBelongsToThrough;

class ActualVisit extends Model
{
    use SoftDeletes;
    use ModelAvailability;
    use ModelExportable;
    use ModelImportable;
    use PdfExportable;
    use SendMail;
    use TraitsBelongsToThrough;

    // public $timestamps = true;
    // protected $guard_name = 'api';

    protected $table = 'actual_visits';

    protected $fillable = [
        'plan_id',
        'user_id',
        'line_id',
        'div_id',
        'brick_id',
        'acc_type_id',
        'speciality_id',
        'end_visit_date',
        'account_id',
        'account_dr_id',
        'shift_id',
        'pharmacy_type_id',
        'visit_type_id',
        'visit_date',
        'end_visit_time',
        'll',
        'lg',
        'll_start',
        'lg_end',
        'visit_duration',
        'invalid_duration',
        'visit_deviation',
        'visit_address',
        'is_automatic',
        'is_web_visit',
        'new_account_id',
        'new_account_dr_id',
        'double_visit_type_id',
        'visit_status',
        'detailing_time',
        'is_fake_gps',
        'os_version',
        'device_brand',
        'os_type'
    ];

    protected $casts = [
        'deleted_at' => 'datetime',
    ];

    public function doubleLocations()
    {
        return $this->hasMany(DoubleVisitLocation::class, 'visit_id');
    }
    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }

    public function details()
    {
        return $this->morphOne(PlanVisitDetails::class, 'visitable');
    }

    public function doctorFrequency()
    {
        return $this->hasOne(DoctorFrequency::class, 'doctor_id', 'account_dr_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function isDouble()
    {
        return $this->visitType->id == 2;
    }

    public function actualVisitProducts()
    {
        return $this->hasMany(VisitProduct::class, 'visit_id');
    }

    public function actualVisitFeedbacks()
    {
        return $this->hasMany(ActualDoubleFeedback::class, 'visit_id');
    }

    public function actualVisitedDoctors()
    {
        return $this->hasMany(ActualVisitedDoctor::class, 'visit_id');
    }

    public function actualVisitManagers()
    {
        return $this->hasMany(ActualVisitManager::class, 'visit_id');
    }

    public function actualVisitGiveaways()
    {
        return $this->hasMany(VisitGiveaway::class, 'visit_id');
    }

    public function attachments()
    {
        return $this->morphMany(Attachment::class, 'attachable');
    }

    public function actualDoubleFeedback()
    {
        return $this->hasOne(ActualDoubleFeedback::class, 'visit_id');
    }

    public function detailing()
    {
        return $this->hasOne(Statistic::class, 'visit_id');
    }

    public function line()
    {
        return $this->belongsTo(Line::class);
    }

    public function division()
    {
        return $this->belongsTo(LineDivision::class, 'div_id');
    }

    public function brick()
    {
        return $this->belongsTo(Brick::class);
    }


    public function accountType()
    {
        return $this->belongsTo(AccountType::class, 'acc_type_id');
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function doctor()
    {
        return $this->belongsTo(Doctor::class, 'account_dr_id');
    }

    public function classType()
    {
        return $this->belongsToThrough(
            Classes::class,
            Doctor::class,
            foreignKeyLookup: [
                Doctor::class => 'account_dr_id',
                Classes::class => 'class_id'
            ],
        );
    }

    public function speciality(): BelongsToThrough
    {
        return $this->belongsToThrough(
            Speciality::class,
            Doctor::class,
            foreignKeyLookup: [
                Doctor::class => 'account_dr_id',
            ],
        );
    }


    public function visitType()
    {
        return $this->belongsTo(VisitType::class, 'visit_type_id');
    }

    public function planVisit()
    {
        return $this->belongsTo(PlanVisit::class, 'plan_id');
    }

    public static function getActualVisitsOfAuthUserAgainstCollectionOfAccountIds(Collection $collection, $needle = 'account_dr_id')
    {
        /**@var User $user */
        $user = Auth::user();
        $users = collect([]);
        $division_type = DivisionType::where('last_level', '=', 1)->value('id');
        if ($user->hasRole('admin') || $user->hasRole('sub admin') || $user->hasRole('Gemstone Admin')) {
            $users = LineDivisionUser::where("from_date", "<=", now())
                ->where(fn($q) => $q->where('to_date', '>', (string)Carbon::now())
                    ->orWhere('to_date', null))->get()->pluck('user_id')->unique()->values();
        } else {
            // $users = $user->indexPerUser($user);
            $divisions = $user->allBelowDivisions()->where('is_kol', 0)
                ->where('division_type_id', $division_type)->unique('id')->pluck('id')->toArray();
            collect($divisions)->each(function ($division) use ($users) {
                $div = LineDivision::find($division);
                $users = $users->push($div->users);
            });
            $users = $users->collapse()->pluck('id')->unique()->values()->toArray();
        }
        $data = collect([]);

        $collection->chunk(1000)->each(function ($accounts) use (&$data, $needle, $users) {
            $data = $data->merge(ActualVisit::select('id', 'user_id', 'account_id', 'account_dr_id', 'visit_date')
                ->whereIntegerInRaw('user_id', $users)
                ->whereIntegerInRaw($needle, $accounts)
                ->whereBetween(
                    'visit_date',
                    [Carbon::now()->startOfMonth()->toDateString(), Carbon::now()->endofMonth()->toDateString()]
                )->get());
        });


        return $data;
    }

    public static function getActualVisitsPerAccountType(AccountType $accountType, ?Carbon $from = null, ?Carbon $to = null, ?User $selectedUser = null)
    {
        $fromDate = $from ?? Carbon::now();
        $toDate = $to ?? Carbon::now();
        /**@var User $user */
        $user = $selectedUser ?? Auth::user();
        $users = collect([]);
        $division_type = DivisionType::where('last_level', '=', 1)->value('id');
        if ($user->hasRole('admin') || $user->hasRole('sub admin') || $user->hasRole('Gemstone Admin')) {
            $users = LineDivisionUser::where("from_date", "<=", $fromDate)
                ->where(fn($q) => $q->where('to_date', '>', (string)$toDate)
                    ->orWhere('to_date', null))->get()->pluck('user_id')->unique()->values();
        } else {
            // $users = $user->indexPerUser($user);
            $divisions = $user->allBelowDivisions()->where('is_kol', 0)
                ->where('division_type_id', $division_type)->unique('id')->pluck('id')->toArray();
            collect($divisions)->each(function ($division) use ($users) {
                $div = LineDivision::find($division);
                $users = $users->push($div->users);
            });
            $users = $users->collapse()->pluck('id')->unique()->values()->toArray();
        }
        return ActualVisit::distinct('account_id')
            ->where('acc_type_id', $accountType->id)
            ->whereIntegerInRaw('user_id', $users)
            ->whereBetween(
                'visit_date',
                [$fromDate->startOfMonth()->toDateString(), $toDate->endofMonth()->toDateString()]
            )->get();
    }

    public static function getFrequencyPerAccountType(AccountType $accountType, ?Carbon $from = null, ?Carbon $to = null, ?User $selectedUser = null)
    {
        $fromDate = $from ?? Carbon::now();
        $toDate = $to ?? Carbon::now();
        /**@var User $user */
        $user = $selectedUser ?? Auth::user();
        $users = collect([]);
        $division_type = DivisionType::where('last_level', '=', 1)->value('id');
        if ($user->hasRole('admin') || $user->hasRole('sub admin') || $user->hasRole('Gemstone Admin')) {
            $users = LineDivisionUser::where("from_date", "<=", $fromDate)
                ->where(fn($q) => $q->where('to_date', '>', (string)$toDate)
                    ->orWhere('to_date', null))->get()->pluck('user_id')->unique()->values();
        } else {
            // $users = $user->indexPerUser($user);
            $divisions = $user->allBelowDivisions()->where('is_kol', 0)
                ->where('division_type_id', $division_type)->unique('id')->pluck('id')->toArray();
            collect($divisions)->each(function ($division) use ($users) {
                $div = LineDivision::find($division);
                $users = $users->push($div->users);
            });
            $users = $users->collapse()->pluck('id')->unique()->values()->toArray();
        }
        return ActualVisit::where('acc_type_id', $accountType->id)
            ->whereIntegerInRaw('user_id', $users)
            ->whereBetween(
                'visit_date',
                [$fromDate->startOfMonth()->toDateString(), $toDate->endofMonth()->toDateString()]
            )->get();
    }

    public static function unplannedVisits(User $user, $visits)
    {
        $color = "brown";
        return $visits->whereNull('plan_id')->values()->map(function ($item) use ($color) {
            return [
                'color' => $color,
                'start' => Carbon::parse($item->visit_date)->toDateTimeString(),
                'end' => Carbon::parse($item->visit_date)->toDateTimeString(),
                'name' => $item->doctor ? $item->account . ' ( ' . $item->doctor . ' )' : $item->account,
                'category' => $item->shift ?? Shift::find($item->acc_shift_id)?->name,
                'timed' => true
            ];
        });
    }

    public static function doubleVisitsCalendar(User $user, $visits)
    {
        return $visits->where('visit_type_id', 2)->values()->map(function ($item) {
            $color = "black";
            return [
                'color' => $color,
                'start' => Carbon::parse($item->visit_date)->toDateTimeString(),
                'end' => Carbon::parse($item->visit_date)->toDateTimeString(),
                'name' => $item->doctor ? $item->account . ' ( ' . $item->doctor . ' )' : $item->account,
                'category' => $item->shift ?? Shift::find($item->acc_shift_id)?->name,
                'timed' => true
            ];
        });
    }
}
