<?php

namespace App\Http\Requests;

use App\Account;
use App\AccountLines;
use App\ActualVisit;
use App\ActualVisitSetting;
use App\AvRequiredInput;
use App\DivisionType;
use App\Doctor;
use App\ErrorMessages;
use App\Exceptions\CrmException;
use App\Helpers\CrmExcelDate;
use App\Line;
use App\Models\AccountTypeDistance;
use App\Models\PlanLevel;
use App\Models\UnplannedVisitNumber;
use App\PlanSetting;
use App\PlanVisit;
use App\Product;
use App\Services\DifferenceBetweenTwoCoordinates;
use App\Services\FrequencyTypeValidationService;
use App\Setting;
use App\UserActualStartDay;
use App\Vacation;
use Carbon\CarbonPeriod;
use Exception;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class ActualVisitPerAccountRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        /**@var User $user */
        $user = Auth::user();
        $accept_visits_within_vacations = ActualVisitSetting::where('key', 'accept_visits_within_vacations')->value('value');
        $accept_officework_with_visits = ActualVisitSetting::where('key', 'accept_officework_with_visits')->value('value');
        $actual_after_meet_frequency = ActualVisitSetting::where('key', 'actual_after_meet_frequency')->value('value');
        $validDistance = AccountTypeDistance::where('type_id', $this->account_type_id)->value('distance');
        $withLocation = Setting::where('key', 'location_gmaps')->value('value') == 'yes';
        $withDeviation = ActualVisitSetting::where('key', 'allow_actual_with_deviation')->value('value') == 'Yes';






        $planLevel = PlanLevel::where(fn($q) => $q->whereNull('line_id')->orWhere('line_id', $this->line_id))->first()->level;

        $inputs = AvRequiredInput::where('select', 1)->get();

        $rules = [
            'line_id' => ['required', 'integer', 'exists_not_soft_deleted:lines,id'],
            'plan_id' => ['nullable', function ($attribute, $value, $fail) use ($user) {
                $max_actual_visits = UnplannedVisitNumber::where(fn($q) => $q->where('line_id', $this->line_id)
                    ->orWhere('line_id', null))->value('number');

                $num_of_actual_per_day = DB::table('actual_visits')
                    ->where('user_id', $user)
                    ->whereNull('plan_id')
                    ->where(DB::raw("(DATE_FORMAT(visit_date,'%Y-%m-%d'))"), Carbon::now()->toDateString())->count();
                if ($max_actual_visits == (string)$num_of_actual_per_day) {
                    throw new Exception('Sorry , You can\'t exceed limit of actual direct per day');
                }
            }],
            'div_id' => ['required', 'integer', 'exists_not_soft_deleted:line_divisions,id'],
            'brick_id' => ['required'],
            'account_id' => [
                'required',
                'integer',
                'exists_not_soft_deleted:accounts,id',
                // function ($attribute, $value, $fail) use ($user) {
                //     $exists = DB::table('actual_visits')
                //         ->select('actual_visits.*')
                //         ->where('user_id', $user->id)
                //         ->where(DB::raw("(DATE_FORMAT(visit_date,'%Y-%m-%d'))"), Carbon::parse($this->visit_date)->toDateString())
                //         ->where('account_id', $value)->exists();
                //     if ($exists) $fail("The " . $attribute . " is visited today");
                // },
                function ($attribute, $value, $fail) use ($user) {
                    if (
                        PlanSetting::where('key', 'plan_disapproved_close_actual')->value('value') == 'yes' &&
                        $this->plan_id != null
                    ) {
                        $exists = PlanVisit::where('user_id', $user->id)
                            ->where(DB::raw("(DATE_FORMAT(visit_date,'%Y-%m-%d'))"), Carbon::parse($this->visit_date)->toDateString())
                            ->where('account_id', $value)
                            ->whereHas('details', fn($q) => $q->where('approval', 0))->exists();
                        if ($exists) $fail("The " . $attribute . " Disapproved from plan");
                    }
                },
                function ($attribute, $value, $fail) use ($validDistance, $withLocation, $withDeviation) {
                    if ($validDistance && $withLocation && $withDeviation) {
                        $account = AccountLines::where('account_id', $value)->where('line_id', $this->line_id)->where('line_division_id', $this->div_id)->first();
                        if (!isNullable($account->ll) && !isNullable($account->lg)) {
                            $distance = (int)(new DifferenceBetweenTwoCoordinates)->distanceBetweenTwoCoordinates($this->ll, $this->lg, $account->ll, $account->lg, 'M');
                            if ($distance > $validDistance) {
                                $fail(ErrorMessages::where("slug", "deviation")->value("message_en") . $validDistance);
                            }
                        }
                    }
                }
            ],
            'account_dr_ids' => [
                'required',
                'array',
                'exists_not_soft_deleted:doctors,id',
            ],
            'account_dr_ids.*' => [
                'required',
                'integer',
                function ($attribute, $value, $fail) use ($planLevel) {
                    $exists = DB::table('actual_visits')
                        ->select('actual_visits.*')
                        ->where('user_id', Auth::id())
                        ->where(DB::raw("(DATE_FORMAT(visit_date,'%Y-%m-%d'))"), Carbon::parse($this->visit_date)->toDateString())
                        ->where('account_dr_id', $value)
                        ->where('account_id', $this->account_id)
                        ->first();
                    if ($exists) $fail("Sorry , This Doctor is Visited Today");
                },
                function ($attribute, $value, $fail) use ($actual_after_meet_frequency, $user) {
                    if ($actual_after_meet_frequency === 'No') {
                        $firstOfMonth = Carbon::now()->startOfMonth()->toDateString();
                        $endOfMonth = Carbon::now()->endofMonth()->toDateString();
                        // foreach ($value as $accountDrId) {
                        $frequency = (new FrequencyTypeValidationService)->classType(
                            $this->account_id,
                            $value,
                            $this->line_id,
                            $this->visit_date
                        );
                        $countVisits = ActualVisit::where('user_id', $user->id)->whereBetween(
                            'visit_date',
                            [$firstOfMonth, $endOfMonth]
                        )->where('account_dr_id', $value)->count();
                        $doctor = Doctor::where('id', $value)->value('name');
                        if ($countVisits >= $frequency)
                            throw new Exception("Doctor: " . $doctor . " " . ErrorMessages::where("slug", "meet_frequency")->value("message_en"));
                    }
                    // }
                }
            ],
            'visit_type_id' => ['required', 'integer', 'exists_not_soft_deleted:visit_types,id', function ($attribute, $value, $fail) {
                if ($value == 1 && ActualVisitSetting::where('key', 'accept_single_visit_with_manager_double_plan')->value('value') == 'No') {
                    $doublePlan = PlanVisit::where('account_id', $this->account_id)
                        ->where('visit_type', 2)
                        ->whereDate('visit_date', Carbon::parse($this->visit_date)->toDateString())
                        ->whereHas('details', function ($q) {
                            $q->where('approval', 1);
                        })->first();
                    if ($doublePlan) $fail('There is Double Plan Created with This Doctor on Same Time');
                }
            }],
            'visit_date' => [
                'required',
                'date_format:' . CrmExcelDate::OFFICIAL_FORMAT,
                function ($attribute, $value, $fail) use ($user) {
                    $actualExtraTime = ActualVisitSetting::where('key', 'actual_extra_time')->value('value');
                    $validDate = Carbon::today()->startOfDay()->addHours($actualExtraTime)->toDateTimeString();
                    if ($value > Carbon::today()->endOfDay()->toDateTimeString()) {
                        $fail('The ' . $attribute . ' can\'t be greater than today');
                    }
                    if (
                        $value < Carbon::today()->toDateTimeString() &&
                        Carbon::now()->toDateTimeString() > $validDate &&
                        !UserActualStartDay::where('line_id', $this->line_id)->where(fn($q) => $q->where('user_id', 0)->orWhere('user_id', $user->id))
                            ->where('date', '<=', Carbon::parse($value)->toDateString())->exists() &&
                        !ActualVisitSetting::where('key', 'specific_actual_start_day')->value('value')
                    ) {
                        $fail('Visit date not valid because it exceeded ' . $validDate);
                    }
                    // if (
                    //     $value < Carbon::today()->toDateTimeString() &&
                    //     !UserActualStartDay::where('user_id', $user->id)
                    //         ->where('date', '<=', Carbon::parse($value)->toDateString())->exists()
                    // ) {
                    //     $fail('User Actual Start Day invalid');
                    // }
                },
                function ($attribute, $value, $fail) use ($accept_officework_with_visits, $user) {
                    if ($accept_officework_with_visits == 'No') {
                        $offices = $user->officeWork()->whereDate('date', Carbon::parse($value)->toDateString());
                        if ($offices->exists()) {
                            $offices->get()->each(function ($office) use ($value, $fail, $attribute) {
                                $account = Account::find($this->account_id);
                                if ($office->shift_id == $account->type->shift->id || isNullable($office->shift_id)) {
                                    $fail('This ' . $attribute . ' not valid because user has office work at ' . Carbon::parse($value)->toDateString());
                                }
                            });
                        }
                    }
                },
                function ($attribute, $value, $fail) use ($accept_visits_within_vacations, $user) {
                    if ($accept_visits_within_vacations == 'No') {
                        $month = Carbon::parse($value)->format('m');
                        $year = Carbon::parse($value)->format('Y');
                        $shift = Account::find($this->account_id)->type->shift_id;
                        $vacations = Vacation::select('id', 'user_id', 'from_date', 'to_date', 'full_day', 'shift_id')
                            ->where('user_id', $user->id)
                            ->where(fn($q) => $q->where('shift_id', $shift)->orWhereNull('shift_id'))
                            ->where(fn($q) => $q->where(DB::raw("(DATE_FORMAT(crm_vacations.from_date,'%m'))"), $month)
                                ->orWhereBetween(DB::raw("(DATE_FORMAT(crm_vacations.from_date,'%m'))"), [Carbon::now()->subMonth()->format('m'), Carbon::now()->addMonth(1)->format('m')]))
                            ->whereYear('vacations.from_date', $year)->whereYear('vacations.to_date', $year)
                            ->whereHas('details', function ($q) {
                                $q->where('approval', 1);
                            })->get();
                        foreach ($vacations as $vacation) {
                            $period = CarbonPeriod::create($vacation->from_date, $vacation->to_date);
                            foreach ($period as $date) {
                                if ($date->format('Y-m-d') == Carbon::parse($value)->toDateString()) {
                                    $fail("There is vacation on this " . $attribute . " , so you can't make any visits");
                                }
                            }
                        }
                    }
                }
            ],
            'll' => [Rule::requiredIf(Setting::where('key', 'location_gmaps')->value('value') == 'yes')],
            'lg' => [Rule::requiredIf(Setting::where('key', 'location_gmaps')->value('value') == 'yes'),],

        ];

        if ($this->acc_type_id != 0) {
            $rules['acc_type_id'] = ['required', 'integer', 'exists_not_soft_deleted:account_types,id'];
        }
        $user_division = $user->division(Line::find($this->line_id))?->division_type_id;

        $last_level_division = DivisionType::where('last_level', 1)->first()->id;
        if ($this->visit_type_id == 2) {
            $rules['managers'] = [
                'distinct',
                Rule::requiredIf($last_level_division == $user_division),
                function ($attribute, $value, $fail) use ($last_level_division, $user_division) {
                    if (
                        ActualVisitSetting::where('key', 'accept_single_visit_with_manager_double_plan')->value('value') == 'No'
                        && $last_level_division == $user_division
                    ) {
                        $doublePlan = PlanVisit::where('account_id', $this->account_id)
                            ->where('visit_type', 2)
                            ->whereDate('visit_date', Carbon::parse($this->visit_date)->toDateString())
                            ->whereHas('details', function ($q) {
                                $q->where('approval', 1);
                            })->first();
                        if ($doublePlan && !in_array($doublePlan?->user_id, $value)) {
                            $fail('Wrong Manager Selected');
                        }
                    }
                }
            ];
        }
        $rules['products.*.product_id'] = [
            'required',
            'integer',
            'not_in:0',
            function ($attribute, $value, $fail) {
                $exists = '';
                $exists = Product::find($value)->exists();
                if (!$exists) $fail($attribute . " is invalid");
            },
            'unique:actual_visit_products,product_id,NULL,id,visit_id,' . $this->request->get('visit_id')
        ];
        $rules['products.*.samples'] = ['required', 'integer'];
        $rules['products.*.vFeedback_id'] = ['required', 'integer', 'not_in:0', 'exists_not_soft_deleted:visit_feedbacks,id'];

        foreach ($inputs as $input) {
            if ($input->name == "giveaway") {
                $rules['giveaways'] = ['required', 'array', 'min:1'];
                $rules['giveaways.*.giveaway_id'] = [
                    'required',
                    'integer',
                    'not_in:0',
                    'exists_not_soft_deleted:giveaways,id',
                    'unique:actual_visit_giveaways,giveaway_id,NULL,id,visit_id,' . $this->request->get('visit_id')
                ];
                $rules['giveaways.*.units'] = ['required', 'integer', 'not_in:0'];
            }
            if ($input->name == "product_comment") {
                $rules['products.*.notes'] = ['required', 'string'];
            }
            if ($input->name == "product_followup") {
                $rules['products.*.follow_up'] = ['required', 'string'];
            }
            if ($input->name == "product_market_feedback") {
                $rules['products.*.market_feedback'] = ['required', 'string'];
            }
            if ($input->name == "attachment") {
                $rules['attachments'] = ['required', 'array', 'min:1'];
            }
        }
        return $rules;
    }
}
