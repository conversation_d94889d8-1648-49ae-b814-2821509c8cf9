<?php

namespace App\Console\Commands;

use App\LineDivParent;
use App\LineDivision;
use App\Sale;
use App\SaleDetail;
use App\Services\Enums\Ceiling;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

/**
 * Comprehensive Distribution Audit Report Command
 * 
 * Generates detailed audit reports for distribution integrity validation
 * including LineDivision hierarchy, 90/10 split verification, ceiling sales
 * processing, and referential integrity checks.
 * 
 * Compatible with Laravel Sail and Octane.
 */
class GenerateDistributionAuditReportCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'distribution:generate-audit-report
                            {--from-date= : Start date for audit (YYYY-MM-DD)}
                            {--to-date= : End date for audit (YYYY-MM-DD)}
                            {--distribution-type= : Distribution type (1=Private Pharmacy, 2=Store, 3=Local Chain)}
                            {--product-ids= : Comma-separated product IDs to filter}
                            {--distributor-ids= : Comma-separated distributor IDs to filter}
                            {--tolerance=0.001 : Tolerance for rounding errors}
                            {--format=table : Output format (table, json, csv)}
                            {--export-file= : Export results to file (without extension)}
                            {--include-hierarchy : Include LineDivision hierarchy validation}
                            {--include-split-analysis : Include 90/10 split analysis}
                            {--include-ceiling-analysis : Include ceiling sales analysis}
                            {--include-referential : Include referential integrity checks}
                            {--summary-only : Show only summary statistics}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate comprehensive audit report for distribution integrity validation including hierarchy, split algorithms, ceiling processing, and referential integrity';

    /**
     * Tolerance for rounding errors
     */
    private float $tolerance;

    /**
     * Output format
     */
    private string $format;

    /**
     * Export filename
     */
    private ?string $exportFile;

    /**
     * Include flags
     */
    private bool $includeHierarchy;
    private bool $includeSplitAnalysis;
    private bool $includeCeilingAnalysis;
    private bool $includeReferential;
    private bool $summaryOnly;

    /**
     * Audit results storage
     */
    private array $auditResults = [];

    /**
     * Summary statistics
     */
    private array $summaryStats = [
        'total_sales_analyzed' => 0,
        'total_distributed_sales' => 0,
        'total_above_sales' => 0,
        'total_below_sales' => 0,
        'total_distribution_errors' => 0,
        'total_hierarchy_errors' => 0,
        'total_split_errors' => 0,
        'total_ceiling_errors' => 0,
        'total_referential_errors' => 0,
        'distribution_integrity_score' => 0.0,
    ];

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        try {
            // Initialize options
            $this->initializeOptions();

            // Validate input parameters
            if (!$this->validateInput()) {
                return Command::FAILURE;
            }

            $this->printHeader();

            // Initialize audit results
            $this->initializeAuditResults();

            // Run comprehensive audit
            $this->runComprehensiveAudit();

            // Calculate summary statistics
            $this->calculateSummaryStatistics();

            // Display results
            $this->displayResults();

            // Export results if requested
            if ($this->exportFile) {
                $this->exportResults();
            }

            // Log audit summary
            $this->logAuditSummary();

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('❌ ERROR: ' . $e->getMessage());
            Log::error('Distribution audit report generation failed', [
                'error' => $e->getMessage(),
                'options' => $this->options()
            ]);
            return Command::FAILURE;
        }
    }

    /**
     * Initialize command options
     */
    private function initializeOptions(): void
    {
        $this->tolerance = (float) $this->option('tolerance');
        $this->format = $this->option('format');
        $this->exportFile = $this->option('export-file');
        $this->includeHierarchy = $this->option('include-hierarchy');
        $this->includeSplitAnalysis = $this->option('include-split-analysis');
        $this->includeCeilingAnalysis = $this->option('include-ceiling-analysis');
        $this->includeReferential = $this->option('include-referential');
        $this->summaryOnly = $this->option('summary-only');
    }

    /**
     * Validate input parameters
     */
    private function validateInput(): bool
    {
        $fromDate = $this->option('from-date');
        $toDate = $this->option('to-date');

        if (!$fromDate || !$toDate) {
            $this->error('Both --from-date and --to-date are required');
            $this->line('');
            $this->line('Example: php artisan distribution:generate-audit-report --from-date=2024-01-01 --to-date=2024-01-31');
            return false;
        }

        try {
            $from = Carbon::createFromFormat('Y-m-d', $fromDate);
            $to = Carbon::createFromFormat('Y-m-d', $toDate);

            if ($from->gt($to)) {
                $this->error('from-date cannot be after to-date');
                return false;
            }
        } catch (\Exception $e) {
            $this->error('Invalid date format. Use YYYY-MM-DD');
            return false;
        }

        $distributionType = $this->option('distribution-type');
        if ($distributionType && !in_array((int)$distributionType, [1, 2, 3])) {
            $this->error('distribution-type must be 1, 2, or 3');
            return false;
        }

        if ($this->tolerance < 0) {
            $this->error('tolerance must be a positive number');
            return false;
        }

        if (!in_array($this->format, ['table', 'json', 'csv'])) {
            $this->error('format must be table, json, or csv');
            return false;
        }

        return true;
    }

    /**
     * Print command header
     */
    private function printHeader(): void
    {
        $this->info('=== DISTRIBUTION INTEGRITY AUDIT REPORT ===');
        $this->line('Generating comprehensive audit report for distribution integrity...');
        $this->line('Date Range: ' . $this->option('from-date') . ' to ' . $this->option('to-date'));
        $this->line('Tolerance: ' . $this->tolerance);
        $this->line('Output Format: ' . $this->format);
        
        if ($this->option('distribution-type')) {
            $typeName = $this->getDistributionTypeName($this->option('distribution-type'));
            $this->line('Distribution Type: ' . $typeName);
        }
        
        if ($this->exportFile) {
            $this->line('Export File: ' . $this->exportFile);
        }
        
        $this->newLine();
    }

    /**
     * Get distribution type name
     */
    private function getDistributionTypeName(string $type): string
    {
        return match($type) {
            '1' => 'Private Pharmacy',
            '2' => 'Store',
            '3' => 'Local Chain',
            default => 'Unknown'
        };
    }

    /**
     * Initialize audit results structure
     */
    private function initializeAuditResults(): void
    {
        $this->auditResults = [
            'metadata' => [
                'generated_at' => now()->toISOString(),
                'date_range' => [
                    'from' => $this->option('from-date'),
                    'to' => $this->option('to-date')
                ],
                'distribution_type' => $this->option('distribution-type'),
                'tolerance' => $this->tolerance,
                'filters' => [
                    'product_ids' => $this->option('product-ids'),
                    'distributor_ids' => $this->option('distributor-ids')
                ]
            ],
            'summary' => [],
            'distribution_integrity' => [],
            'hierarchy_validation' => [],
            'split_analysis' => [],
            'ceiling_analysis' => [],
            'referential_integrity' => [],
            'recommendations' => []
        ];
    }

    /**
     * Run comprehensive audit
     */
    private function runComprehensiveAudit(): void
    {
        $this->info('🔍 Running comprehensive distribution audit...');
        
        // Core distribution integrity validation
        $this->auditDistributionIntegrity();
        
        // LineDivision hierarchy validation
        if ($this->includeHierarchy) {
            $this->auditHierarchyIntegrity();
        }
        
        // 90/10 split analysis for Store strategy
        if ($this->includeSplitAnalysis || $this->option('distribution-type') == '2') {
            $this->auditSplitDistribution();
        }
        
        // Ceiling sales processing analysis
        if ($this->includeCeilingAnalysis) {
            $this->auditCeilingProcessing();
        }
        
        // Referential integrity checks
        if ($this->includeReferential) {
            $this->auditReferentialIntegrity();
        }
        
        // Generate recommendations
        $this->generateRecommendations();
    }

    /**
     * Build base query for sales filtering
     */
    private function buildBaseQuery()
    {
        $query = Sale::whereBetween('date', [
            $this->option('from-date'),
            $this->option('to-date')
        ]);

        // Apply product filter
        if ($this->option('product-ids')) {
            $productIds = array_map('intval', explode(',', $this->option('product-ids')));
            $query->whereIn('product_id', $productIds);
        }

        // Apply distributor filter
        if ($this->option('distributor-ids')) {
            $distributorIds = array_map('intval', explode(',', $this->option('distributor-ids')));
            $query->whereIn('distributor_id', $distributorIds);
        }

        return $query;
    }

    /**
     * Audit core distribution integrity
     */
    private function auditDistributionIntegrity(): void
    {
        $this->line('Auditing core distribution integrity...');

        // Get totals before distribution (ABOVE sales)
        $beforeTotals = $this->calculateBeforeDistributionTotals();

        // Get totals after distribution (BELOW + DISTRIBUTED sales)
        $afterTotals = $this->calculateAfterDistributionTotals();

        // Calculate integrity metrics
        $integrityResults = $this->validateDistributionIntegrity($beforeTotals, $afterTotals);

        // Analyze distribution patterns
        $distributionPatterns = $this->analyzeDistributionPatterns();

        // Store results
        $this->auditResults['distribution_integrity'] = [
            'before_totals' => $beforeTotals,
            'after_totals' => $afterTotals,
            'integrity_validation' => $integrityResults,
            'distribution_patterns' => $distributionPatterns,
            'ceiling_status_breakdown' => $this->getCeilingStatusBreakdown()
        ];

        // Update summary stats
        $this->summaryStats['total_sales_analyzed'] = $beforeTotals['records'] + $afterTotals['records'];
        $this->summaryStats['total_above_sales'] = $beforeTotals['records'];
        $this->summaryStats['total_distributed_sales'] = $this->getDistributedSalesCount();
        $this->summaryStats['total_below_sales'] = $this->getBelowSalesCount();
        $this->summaryStats['total_distribution_errors'] = count($integrityResults['errors']);

        $this->info('✓ Distribution integrity audit completed');
    }

    /**
     * Calculate totals before distribution (ABOVE sales)
     */
    private function calculateBeforeDistributionTotals(): array
    {
        $query = $this->buildBaseQuery()
            ->where('ceiling', Ceiling::ABOVE->value);

        $totals = $query->selectRaw('
            SUM(quantity) as total_quantity,
            SUM(value) as total_value,
            SUM(bonus) as total_bonus,
            COUNT(*) as total_records
        ')->first();

        return [
            'quantity' => (float)($totals->total_quantity ?? 0),
            'value' => (float)($totals->total_value ?? 0),
            'bonus' => (float)($totals->total_bonus ?? 0),
            'records' => (int)($totals->total_records ?? 0)
        ];
    }

    /**
     * Calculate totals after distribution (BELOW + DISTRIBUTED sales)
     */
    private function calculateAfterDistributionTotals(): array
    {
        $query = $this->buildBaseQuery()
            ->whereIn('ceiling', [Ceiling::BELOW->value, Ceiling::DISTRIBUTED->value]);

        $totals = $query->selectRaw('
            SUM(quantity) as total_quantity,
            SUM(value) as total_value,
            SUM(bonus) as total_bonus,
            COUNT(*) as total_records
        ')->first();

        return [
            'quantity' => (float)($totals->total_quantity ?? 0),
            'value' => (float)($totals->total_value ?? 0),
            'bonus' => (float)($totals->total_bonus ?? 0),
            'records' => (int)($totals->total_records ?? 0)
        ];
    }

    /**
     * Validate distribution integrity
     */
    private function validateDistributionIntegrity(array $beforeTotals, array $afterTotals): array
    {
        $result = [
            'passed' => true,
            'errors' => [],
            'warnings' => [],
            'metrics' => []
        ];

        // Compare quantities
        $quantityDiff = abs($beforeTotals['quantity'] - $afterTotals['quantity']);
        if ($quantityDiff > $this->tolerance) {
            $result['passed'] = false;
            $result['errors'][] = [
                'type' => 'quantity_mismatch',
                'message' => "Quantity mismatch: Before={$beforeTotals['quantity']}, After={$afterTotals['quantity']}, Difference={$quantityDiff}",
                'severity' => 'high',
                'before' => $beforeTotals['quantity'],
                'after' => $afterTotals['quantity'],
                'difference' => $quantityDiff
            ];
        } elseif ($quantityDiff > 0) {
            $result['warnings'][] = [
                'type' => 'minor_quantity_difference',
                'message' => "Minor quantity difference within tolerance: {$quantityDiff}",
                'difference' => $quantityDiff
            ];
        }

        // Compare values
        $valueDiff = abs($beforeTotals['value'] - $afterTotals['value']);
        if ($valueDiff > $this->tolerance) {
            $result['passed'] = false;
            $result['errors'][] = [
                'type' => 'value_mismatch',
                'message' => "Value mismatch: Before={$beforeTotals['value']}, After={$afterTotals['value']}, Difference={$valueDiff}",
                'severity' => 'high',
                'before' => $beforeTotals['value'],
                'after' => $afterTotals['value'],
                'difference' => $valueDiff
            ];
        } elseif ($valueDiff > 0) {
            $result['warnings'][] = [
                'type' => 'minor_value_difference',
                'message' => "Minor value difference within tolerance: {$valueDiff}",
                'difference' => $valueDiff
            ];
        }

        // Compare bonuses
        $bonusDiff = abs($beforeTotals['bonus'] - $afterTotals['bonus']);
        if ($bonusDiff > $this->tolerance) {
            $result['passed'] = false;
            $result['errors'][] = [
                'type' => 'bonus_mismatch',
                'message' => "Bonus mismatch: Before={$beforeTotals['bonus']}, After={$afterTotals['bonus']}, Difference={$bonusDiff}",
                'severity' => 'high',
                'before' => $beforeTotals['bonus'],
                'after' => $afterTotals['bonus'],
                'difference' => $bonusDiff
            ];
        } elseif ($bonusDiff > 0) {
            $result['warnings'][] = [
                'type' => 'minor_bonus_difference',
                'message' => "Minor bonus difference within tolerance: {$bonusDiff}",
                'difference' => $bonusDiff
            ];
        }

        // Calculate integrity metrics
        $result['metrics'] = [
            'quantity_accuracy' => $beforeTotals['quantity'] > 0 ?
                (1 - ($quantityDiff / $beforeTotals['quantity'])) * 100 : 100,
            'value_accuracy' => $beforeTotals['value'] > 0 ?
                (1 - ($valueDiff / $beforeTotals['value'])) * 100 : 100,
            'bonus_accuracy' => $beforeTotals['bonus'] > 0 ?
                (1 - ($bonusDiff / $beforeTotals['bonus'])) * 100 : 100,
            'overall_accuracy' => 0
        ];

        $result['metrics']['overall_accuracy'] = (
            $result['metrics']['quantity_accuracy'] +
            $result['metrics']['value_accuracy'] +
            $result['metrics']['bonus_accuracy']
        ) / 3;

        return $result;
    }

    /**
     * Analyze distribution patterns
     */
    private function analyzeDistributionPatterns(): array
    {
        $patterns = [];

        // Analyze distribution by ceiling status
        $ceilingBreakdown = $this->getCeilingStatusBreakdown();
        $patterns['ceiling_distribution'] = $ceilingBreakdown;

        // Analyze distribution by product
        $productBreakdown = $this->getProductDistributionBreakdown();
        $patterns['product_distribution'] = $productBreakdown;

        // Analyze distribution by distributor
        $distributorBreakdown = $this->getDistributorBreakdown();
        $patterns['distributor_distribution'] = $distributorBreakdown;

        return $patterns;
    }

    /**
     * Get ceiling status breakdown
     */
    private function getCeilingStatusBreakdown(): array
    {
        return $this->buildBaseQuery()
            ->selectRaw('
                ceiling,
                COUNT(*) as count,
                SUM(quantity) as total_quantity,
                SUM(value) as total_value,
                SUM(bonus) as total_bonus
            ')
            ->groupBy('ceiling')
            ->get()
            ->mapWithKeys(function ($item) {
                return [$item->ceiling => [
                    'count' => (int)$item->count,
                    'quantity' => (float)$item->total_quantity,
                    'value' => (float)$item->total_value,
                    'bonus' => (float)$item->total_bonus
                ]];
            })
            ->toArray();
    }

    /**
     * Get product distribution breakdown
     */
    private function getProductDistributionBreakdown(): array
    {
        return $this->buildBaseQuery()
            ->selectRaw('
                product_id,
                COUNT(*) as count,
                SUM(quantity) as total_quantity,
                SUM(value) as total_value
            ')
            ->groupBy('product_id')
            ->orderByDesc('total_quantity')
            ->limit(20) // Top 20 products
            ->get()
            ->map(function ($item) {
                return [
                    'product_id' => $item->product_id,
                    'count' => (int)$item->count,
                    'quantity' => (float)$item->total_quantity,
                    'value' => (float)$item->total_value
                ];
            })
            ->toArray();
    }

    /**
     * Get distributor breakdown
     */
    private function getDistributorBreakdown(): array
    {
        return $this->buildBaseQuery()
            ->selectRaw('
                distributor_id,
                COUNT(*) as count,
                SUM(quantity) as total_quantity,
                SUM(value) as total_value
            ')
            ->groupBy('distributor_id')
            ->orderByDesc('total_quantity')
            ->limit(20) // Top 20 distributors
            ->get()
            ->map(function ($item) {
                return [
                    'distributor_id' => $item->distributor_id,
                    'count' => (int)$item->count,
                    'quantity' => (float)$item->total_quantity,
                    'value' => (float)$item->total_value
                ];
            })
            ->toArray();
    }

    /**
     * Get distributed sales count
     */
    private function getDistributedSalesCount(): int
    {
        return $this->buildBaseQuery()
            ->where('ceiling', Ceiling::DISTRIBUTED->value)
            ->count();
    }

    /**
     * Get below sales count
     */
    private function getBelowSalesCount(): int
    {
        return $this->buildBaseQuery()
            ->where('ceiling', Ceiling::BELOW->value)
            ->count();
    }

    /**
     * Audit LineDivision hierarchy integrity
     */
    private function auditHierarchyIntegrity(): void
    {
        $this->line('Auditing LineDivision hierarchy integrity...');

        $hierarchyResults = [
            'temporal_relationships' => $this->validateTemporalRelationships(),
            'orphaned_divisions' => $this->findOrphanedDivisions(),
            'circular_references' => $this->detectCircularReferences(),
            'hierarchy_completeness' => $this->validateHierarchyCompleteness()
        ];

        $this->auditResults['hierarchy_validation'] = $hierarchyResults;

        // Count hierarchy errors
        $errorCount = 0;
        foreach ($hierarchyResults as $category => $results) {
            if (isset($results['errors'])) {
                $errorCount += count($results['errors']);
            }
        }
        $this->summaryStats['total_hierarchy_errors'] = $errorCount;

        $this->info('✓ Hierarchy integrity audit completed');
    }

    /**
     * Validate temporal relationships in LineDivParent
     */
    private function validateTemporalRelationships(): array
    {
        $errors = [];
        $warnings = [];

        // Check for overlapping date ranges
        $overlappingRanges = LineDivParent::select('line_div_id')
            ->selectRaw('COUNT(*) as count')
            ->whereNotNull('from_date')
            ->whereNotNull('to_date')
            ->groupBy('line_div_id')
            ->havingRaw('COUNT(*) > 1')
            ->get();

        foreach ($overlappingRanges as $range) {
            $relationships = LineDivParent::where('line_div_id', $range->line_div_id)
                ->whereNotNull('from_date')
                ->whereNotNull('to_date')
                ->orderBy('from_date')
                ->get();

            for ($i = 0; $i < $relationships->count() - 1; $i++) {
                $current = $relationships[$i];
                $next = $relationships[$i + 1];

                if ($current->to_date >= $next->from_date) {
                    $errors[] = [
                        'type' => 'overlapping_temporal_range',
                        'line_div_id' => $current->line_div_id,
                        'current_range' => "{$current->from_date} to {$current->to_date}",
                        'overlapping_range' => "{$next->from_date} to {$next->to_date}",
                        'message' => 'Overlapping temporal relationships detected'
                    ];
                }
            }
        }

        // Check for active relationships without to_date
        $activeRelationships = LineDivParent::whereNull('to_date')->count();

        return [
            'errors' => $errors,
            'warnings' => $warnings,
            'metrics' => [
                'total_relationships' => LineDivParent::count(),
                'active_relationships' => $activeRelationships,
                'overlapping_ranges' => count($errors)
            ]
        ];
    }

    /**
     * Find orphaned divisions
     */
    private function findOrphanedDivisions(): array
    {
        $errors = [];

        // Find LineDivisions without parent relationships
        $orphanedDivisions = LineDivision::whereNotExists(function ($query) {
            $query->select(DB::raw(1))
                ->from('line_div_parents')
                ->whereColumn('line_div_parents.line_div_id', 'line_divisions.id')
                ->whereNull('line_div_parents.deleted_at');
        })->get();

        foreach ($orphanedDivisions as $division) {
            $errors[] = [
                'type' => 'orphaned_division',
                'division_id' => $division->id,
                'division_name' => $division->name,
                'message' => 'Division has no parent relationships'
            ];
        }

        return [
            'errors' => $errors,
            'metrics' => [
                'total_divisions' => LineDivision::count(),
                'orphaned_divisions' => count($errors)
            ]
        ];
    }

    /**
     * Detect circular references in hierarchy
     */
    private function detectCircularReferences(): array
    {
        $errors = [];
        $visited = [];
        $recursionStack = [];

        $relationships = LineDivParent::whereNull('to_date')->get();

        foreach ($relationships as $relationship) {
            if (!isset($visited[$relationship->line_div_id])) {
                $this->detectCircularReferencesRecursive(
                    $relationship->line_div_id,
                    $visited,
                    $recursionStack,
                    $errors,
                    $relationships
                );
            }
        }

        return [
            'errors' => $errors,
            'metrics' => [
                'circular_references_found' => count($errors)
            ]
        ];
    }

    /**
     * Recursive helper for circular reference detection
     */
    private function detectCircularReferencesRecursive(
        int $divisionId,
        array &$visited,
        array &$recursionStack,
        array &$errors,
        Collection $relationships
    ): void {
        $visited[$divisionId] = true;
        $recursionStack[$divisionId] = true;

        $children = $relationships->where('parent_id', $divisionId);

        foreach ($children as $child) {
            if (!isset($visited[$child->line_div_id])) {
                $this->detectCircularReferencesRecursive(
                    $child->line_div_id,
                    $visited,
                    $recursionStack,
                    $errors,
                    $relationships
                );
            } elseif (isset($recursionStack[$child->line_div_id])) {
                $errors[] = [
                    'type' => 'circular_reference',
                    'parent_id' => $divisionId,
                    'child_id' => $child->line_div_id,
                    'message' => 'Circular reference detected in hierarchy'
                ];
            }
        }

        unset($recursionStack[$divisionId]);
    }

    /**
     * Validate hierarchy completeness
     */
    private function validateHierarchyCompleteness(): array
    {
        $warnings = [];
        $metrics = [];

        // Check for divisions without children at non-leaf levels
        $divisionsWithoutChildren = LineDivision::whereNotExists(function ($query) {
            $query->select(DB::raw(1))
                ->from('line_div_parents')
                ->whereColumn('line_div_parents.parent_id', 'line_divisions.id')
                ->whereNull('line_div_parents.deleted_at');
        })->count();

        $metrics['divisions_without_children'] = $divisionsWithoutChildren;
        $metrics['total_divisions'] = LineDivision::count();
        $metrics['completeness_ratio'] = $metrics['total_divisions'] > 0 ?
            (($metrics['total_divisions'] - $divisionsWithoutChildren) / $metrics['total_divisions']) * 100 : 100;

        return [
            'warnings' => $warnings,
            'metrics' => $metrics
        ];
    }

    /**
     * Audit 90/10 split distribution
     */
    private function auditSplitDistribution(): void
    {
        $this->line('Auditing 90/10 split distribution...');

        $splitResults = [
            'ratio_validation' => $this->validateSplitRatios(),
            'distribution_accuracy' => $this->validateDistributionAccuracy(),
            'split_completeness' => $this->validateSplitCompleteness()
        ];

        $this->auditResults['split_analysis'] = $splitResults;

        // Count split errors
        $errorCount = 0;
        foreach ($splitResults as $category => $results) {
            if (isset($results['errors'])) {
                $errorCount += count($results['errors']);
            }
        }
        $this->summaryStats['total_split_errors'] = $errorCount;

        $this->info('✓ Split distribution audit completed');
    }

    /**
     * Validate split ratios for distributed sales
     */
    private function validateSplitRatios(): array
    {
        $errors = [];
        $warnings = [];

        // Check both BELOW and DISTRIBUTED sales for split ratio validation
        $derivedSales = $this->buildBaseQuery()
            ->whereIn('ceiling', [Ceiling::BELOW->value, Ceiling::DISTRIBUTED->value])
            ->whereNotNull('sale_ids')
            ->get();

        foreach ($derivedSales as $sale) {
            $details = SaleDetail::where('sale_id', $sale->id)->get();

            if ($details->isNotEmpty()) {
                $totalRatio = $details->sum('ratio');

                // Check if total ratio is close to 1.0
                if (abs($totalRatio - 1.0) > $this->tolerance) {
                    $errors[] = [
                        'type' => 'invalid_total_ratio',
                        'sale_id' => $sale->id,
                        'ceiling' => $sale->ceiling,
                        'expected_ratio' => 1.0,
                        'actual_ratio' => $totalRatio,
                        'difference' => abs($totalRatio - 1.0),
                        'message' => 'Total distribution ratio deviates from 1.0'
                    ];
                }

                // Check for negative ratios
                $negativeRatios = $details->where('ratio', '<', 0);
                foreach ($negativeRatios as $detail) {
                    $errors[] = [
                        'type' => 'negative_ratio',
                        'sale_id' => $sale->id,
                        'ceiling' => $sale->ceiling,
                        'detail_id' => $detail->id,
                        'ratio' => $detail->ratio,
                        'message' => 'Negative ratio found in distribution detail'
                    ];
                }

                // Check for ratios > 1
                $excessiveRatios = $details->where('ratio', '>', 1);
                foreach ($excessiveRatios as $detail) {
                    $warnings[] = [
                        'type' => 'excessive_ratio',
                        'sale_id' => $sale->id,
                        'ceiling' => $sale->ceiling,
                        'detail_id' => $detail->id,
                        'ratio' => $detail->ratio,
                        'message' => 'Ratio greater than 1.0 found in distribution detail'
                    ];
                }
            }
        }

        // Validate 90/10 split for Store strategy specifically
        if ($this->option('distribution-type') == '2') {
            $this->validateStoreStrategySplit($errors, $warnings, $derivedSales);
        }

        return [
            'errors' => $errors,
            'warnings' => $warnings,
            'metrics' => [
                'total_derived_sales' => $derivedSales->count(),
                'ratio_errors' => count($errors),
                'ratio_warnings' => count($warnings)
            ]
        ];
    }

    /**
     * Validate Store strategy 90/10 split specifically
     */
    private function validateStoreStrategySplit(array &$errors, array &$warnings, $derivedSales): void
    {
        foreach ($derivedSales as $sale) {
            $details = SaleDetail::where('sale_id', $sale->id)->get();

            if ($details->count() === 2) {
                $ratios = $details->pluck('ratio')->sort()->values();

                // Check if we have approximately 10% and 90% split
                $expectedRatios = [0.1, 0.9];
                $actualRatios = [$ratios[0], $ratios[1]];

                $ratio1Diff = abs($actualRatios[0] - $expectedRatios[0]);
                $ratio2Diff = abs($actualRatios[1] - $expectedRatios[1]);

                if ($ratio1Diff > $this->tolerance || $ratio2Diff > $this->tolerance) {
                    $warnings[] = [
                        'type' => 'non_standard_split_ratio',
                        'sale_id' => $sale->id,
                        'ceiling' => $sale->ceiling,
                        'expected_ratios' => $expectedRatios,
                        'actual_ratios' => $actualRatios,
                        'message' => 'Store strategy should use 90/10 split ratio'
                    ];
                }
            } elseif ($details->count() > 2) {
                $warnings[] = [
                    'type' => 'multiple_split_details',
                    'sale_id' => $sale->id,
                    'ceiling' => $sale->ceiling,
                    'detail_count' => $details->count(),
                    'message' => 'Store strategy typically uses 2-way split (90/10)'
                ];
            }
        }
    }

    /**
     * Validate distribution accuracy
     */
    private function validateDistributionAccuracy(): array
    {
        $errors = [];

        // Check both BELOW and DISTRIBUTED sales for accuracy
        $derivedSales = $this->buildBaseQuery()
            ->whereIn('ceiling', [Ceiling::BELOW->value, Ceiling::DISTRIBUTED->value])
            ->whereNotNull('sale_ids')
            ->get();

        foreach ($derivedSales as $sale) {
            $details = SaleDetail::where('sale_id', $sale->id)->get();

            if ($details->isNotEmpty()) {
                // Check if detail quantities match sale quantity
                $totalDetailQuantity = $details->sum('quantity');
                $quantityDiff = abs($sale->quantity - $totalDetailQuantity);

                if ($quantityDiff > $this->tolerance) {
                    $errors[] = [
                        'type' => 'quantity_mismatch',
                        'sale_id' => $sale->id,
                        'ceiling' => $sale->ceiling,
                        'expected_quantity' => $sale->quantity,
                        'actual_quantity' => $totalDetailQuantity,
                        'difference' => $quantityDiff,
                        'message' => 'Detail quantities do not match sale quantity'
                    ];
                }

                // Check if detail values match sale value
                $totalDetailValue = $details->sum('value');
                $valueDiff = abs($sale->value - $totalDetailValue);

                if ($valueDiff > $this->tolerance) {
                    $errors[] = [
                        'type' => 'value_mismatch',
                        'sale_id' => $sale->id,
                        'ceiling' => $sale->ceiling,
                        'expected_value' => $sale->value,
                        'actual_value' => $totalDetailValue,
                        'difference' => $valueDiff,
                        'message' => 'Detail values do not match sale value'
                    ];
                }
            }
        }

        return [
            'errors' => $errors,
            'metrics' => [
                'accuracy_errors' => count($errors),
                'derived_sales_checked' => $derivedSales->count()
            ]
        ];
    }

    /**
     * Validate split completeness
     */
    private function validateSplitCompleteness(): array
    {
        $warnings = [];

        // Check for derived sales without details
        $belowSalesWithoutDetails = $this->buildBaseQuery()
            ->where('ceiling', Ceiling::BELOW->value)
            ->whereNotNull('sale_ids')
            ->whereDoesntHave('details')
            ->count();

        $distributedSalesWithoutDetails = $this->buildBaseQuery()
            ->where('ceiling', Ceiling::DISTRIBUTED->value)
            ->whereNotNull('sale_ids')
            ->whereDoesntHave('details')
            ->count();

        if ($belowSalesWithoutDetails > 0) {
            $warnings[] = [
                'type' => 'incomplete_below_distribution',
                'count' => $belowSalesWithoutDetails,
                'message' => 'BELOW sales found without distribution details'
            ];
        }

        if ($distributedSalesWithoutDetails > 0) {
            $warnings[] = [
                'type' => 'incomplete_distributed_distribution',
                'count' => $distributedSalesWithoutDetails,
                'message' => 'DISTRIBUTED sales found without distribution details'
            ];
        }

        return [
            'warnings' => $warnings,
            'metrics' => [
                'below_sales_without_details' => $belowSalesWithoutDetails,
                'distributed_sales_without_details' => $distributedSalesWithoutDetails,
                'total_sales_without_details' => $belowSalesWithoutDetails + $distributedSalesWithoutDetails
            ]
        ];
    }

    /**
     * Audit ceiling processing
     */
    private function auditCeilingProcessing(): void
    {
        $this->line('Auditing ceiling sales processing...');

        $ceilingResults = [
            'ceiling_transitions' => $this->validateCeilingTransitions(),
            'private_pharmacy_processing' => $this->validatePrivatePharmacyProcessing(),
            'ceiling_consistency' => $this->validateCeilingConsistency()
        ];

        $this->auditResults['ceiling_analysis'] = $ceilingResults;

        // Count ceiling errors
        $errorCount = 0;
        foreach ($ceilingResults as $category => $results) {
            if (isset($results['errors'])) {
                $errorCount += count($results['errors']);
            }
        }
        $this->summaryStats['total_ceiling_errors'] = $errorCount;

        $this->info('✓ Ceiling processing audit completed');
    }

    /**
     * Validate ceiling transitions
     */
    private function validateCeilingTransitions(): array
    {
        $errors = [];

        // Check for ABOVE sales that incorrectly have sale_ids (should be null)
        $invalidAboveSales = $this->buildBaseQuery()
            ->where('ceiling', Ceiling::ABOVE->value)
            ->whereNotNull('sale_ids')
            ->get();

        foreach ($invalidAboveSales as $sale) {
            $errors[] = [
                'type' => 'invalid_above_sale_ids',
                'sale_id' => $sale->id,
                'ceiling' => $sale->ceiling,
                'sale_ids' => $sale->sale_ids,
                'message' => 'ABOVE sale should not have sale_ids (original sales should have null sale_ids)'
            ];
        }

        // Check for BELOW sales without sale_ids (should reference original ABOVE sale)
        $invalidBelowSales = $this->buildBaseQuery()
            ->where('ceiling', Ceiling::BELOW->value)
            ->whereNull('sale_ids')
            ->get();

        foreach ($invalidBelowSales as $sale) {
            $errors[] = [
                'type' => 'missing_below_sale_ids',
                'sale_id' => $sale->id,
                'ceiling' => $sale->ceiling,
                'message' => 'BELOW sale missing sale_ids reference to original ABOVE sale'
            ];
        }

        // Check for DISTRIBUTED sales without sale_ids (should reference original ABOVE sale)
        $invalidDistributedSales = $this->buildBaseQuery()
            ->where('ceiling', Ceiling::DISTRIBUTED->value)
            ->whereNull('sale_ids')
            ->get();

        foreach ($invalidDistributedSales as $sale) {
            $errors[] = [
                'type' => 'missing_distributed_sale_ids',
                'sale_id' => $sale->id,
                'ceiling' => $sale->ceiling,
                'message' => 'DISTRIBUTED sale missing sale_ids reference to original ABOVE sale'
            ];
        }

        return [
            'errors' => $errors,
            'metrics' => [
                'invalid_above_sales' => count($invalidAboveSales),
                'invalid_below_sales' => count($invalidBelowSales),
                'invalid_distributed_sales' => count($invalidDistributedSales)
            ]
        ];
    }

    /**
     * Validate private pharmacy processing
     */
    private function validatePrivatePharmacyProcessing(): array
    {
        $errors = [];
        $warnings = [];

        // Validate that BELOW and DISTRIBUTED sales reference valid ABOVE sales
        $derivedSales = $this->buildBaseQuery()
            ->whereIn('ceiling', [Ceiling::BELOW->value, Ceiling::DISTRIBUTED->value])
            ->whereNotNull('sale_ids')
            ->get();

        foreach ($derivedSales as $sale) {
            if ($sale->sale_ids) {
                $originalSaleIds = explode(',', $sale->sale_ids);
                foreach ($originalSaleIds as $originalSaleId) {
                    $originalSale = Sale::find(trim($originalSaleId));

                    if (!$originalSale) {
                        $errors[] = [
                            'type' => 'missing_original_sale',
                            'sale_id' => $sale->id,
                            'ceiling' => $sale->ceiling,
                            'missing_original_sale_id' => trim($originalSaleId),
                            'message' => 'Referenced original sale does not exist'
                        ];
                    } elseif ($originalSale->ceiling !== Ceiling::ABOVE->value) {
                        $errors[] = [
                            'type' => 'incorrect_original_status',
                            'sale_id' => $sale->id,
                            'ceiling' => $sale->ceiling,
                            'original_sale_id' => $originalSale->id,
                            'original_ceiling' => $originalSale->ceiling,
                            'message' => 'Referenced sale should be ABOVE status (original sales must have ceiling="1")'
                        ];
                    }
                }
            }
        }

        // For PRIVATE_PHARMACY specifically, validate quantity relationships
        if ($this->option('distribution-type') == '1') {
            $this->validateQuantityRelationships($errors, $warnings);
        }

        return [
            'errors' => $errors,
            'warnings' => $warnings,
            'metrics' => [
                'processing_errors' => count($errors),
                'derived_sales_checked' => $derivedSales->count()
            ]
        ];
    }

    /**
     * Validate quantity relationships between original and derived sales
     */
    private function validateQuantityRelationships(array &$errors, array &$warnings): void
    {
        // Get all ABOVE sales and check if their derived sales quantities sum correctly
        $aboveSales = $this->buildBaseQuery()
            ->where('ceiling', Ceiling::ABOVE->value)
            ->get();

        foreach ($aboveSales as $originalSale) {
            // Find all BELOW and DISTRIBUTED sales that reference this original sale
            $derivedSales = Sale::whereIn('ceiling', [Ceiling::BELOW->value, Ceiling::DISTRIBUTED->value])
                ->where('sale_ids', 'LIKE', "%{$originalSale->id}%")
                ->get();

            if ($derivedSales->isNotEmpty()) {
                $totalDerivedQuantity = $derivedSales->sum('quantity');
                $quantityDiff = abs($originalSale->quantity - $totalDerivedQuantity);

                if ($quantityDiff > $this->tolerance) {
                    $errors[] = [
                        'type' => 'quantity_mismatch_original_derived',
                        'original_sale_id' => $originalSale->id,
                        'original_quantity' => $originalSale->quantity,
                        'derived_total_quantity' => $totalDerivedQuantity,
                        'difference' => $quantityDiff,
                        'derived_sales_count' => $derivedSales->count(),
                        'message' => 'Sum of derived sales quantities does not match original sale quantity'
                    ];
                }
            }
        }
    }

    /**
     * Validate ceiling consistency
     */
    private function validateCeilingConsistency(): array
    {
        $errors = [];

        // Check for null ceiling values
        $nullCeilings = $this->buildBaseQuery()
            ->whereNull('ceiling')
            ->count();

        if ($nullCeilings > 0) {
            $errors[] = [
                'type' => 'null_ceiling_values',
                'count' => $nullCeilings,
                'message' => 'Sales found with null ceiling values'
            ];
        }

        return [
            'errors' => $errors,
            'metrics' => [
                'null_ceilings' => $nullCeilings
            ]
        ];
    }

    /**
     * Audit referential integrity
     */
    private function auditReferentialIntegrity(): void
    {
        $this->line('Auditing referential integrity...');

        $referentialResults = [
            'orphaned_distributions' => $this->findOrphanedDistributions(),
            'missing_original_sales' => $this->findMissingOriginalSales(),
            'invalid_sale_references' => $this->validateSaleReferences()
        ];

        $this->auditResults['referential_integrity'] = $referentialResults;

        // Count referential errors
        $errorCount = 0;
        foreach ($referentialResults as $category => $results) {
            if (isset($results['errors'])) {
                $errorCount += count($results['errors']);
            }
        }
        $this->summaryStats['total_referential_errors'] = $errorCount;

        $this->info('✓ Referential integrity audit completed');
    }

    /**
     * Find orphaned distributions
     */
    private function findOrphanedDistributions(): array
    {
        $errors = [];

        // Check both BELOW and DISTRIBUTED sales for orphaned sale_ids references
        $derivedSales = $this->buildBaseQuery()
            ->whereIn('ceiling', [Ceiling::BELOW->value, Ceiling::DISTRIBUTED->value])
            ->whereNotNull('sale_ids')
            ->get();

        foreach ($derivedSales as $sale) {
            if ($sale->sale_ids) {
                $originalSaleIds = explode(',', $sale->sale_ids);

                foreach ($originalSaleIds as $originalSaleId) {
                    $originalSale = Sale::find(trim($originalSaleId));

                    if (!$originalSale) {
                        $errors[] = [
                            'type' => 'missing_original_sale',
                            'sale_id' => $sale->id,
                            'ceiling' => $sale->ceiling,
                            'missing_original_sale_id' => trim($originalSaleId),
                            'message' => 'Derived sale references non-existent original sale'
                        ];
                    } elseif ($originalSale->ceiling !== Ceiling::ABOVE->value) {
                        $errors[] = [
                            'type' => 'invalid_original_sale_status',
                            'sale_id' => $sale->id,
                            'ceiling' => $sale->ceiling,
                            'original_sale_id' => $originalSale->id,
                            'original_ceiling' => $originalSale->ceiling,
                            'message' => 'Derived sale references sale that is not ABOVE status'
                        ];
                    }
                }
            }
        }

        return [
            'errors' => $errors,
            'metrics' => [
                'orphaned_distributions' => count($errors),
                'derived_sales_checked' => $derivedSales->count()
            ]
        ];
    }

    /**
     * Find missing original sales
     */
    private function findMissingOriginalSales(): array
    {
        $errors = [];

        // Find sales that should have been distributed but are missing distribution records
        $aboveSales = $this->buildBaseQuery()
            ->where('ceiling', Ceiling::ABOVE->value)
            ->get();

        foreach ($aboveSales as $sale) {
            // Check if this sale has corresponding distributed sales
            $distributedSales = Sale::where('sale_ids', 'LIKE', "%{$sale->id}%")
                ->where('ceiling', Ceiling::DISTRIBUTED->value)
                ->count();

            if ($distributedSales === 0) {
                // This might be intentional (below ceiling), so it's a warning rather than error
                // Only flag as error if we can determine it should have been distributed
            }
        }

        return [
            'errors' => $errors,
            'metrics' => [
                'potentially_missing_distributions' => count($errors)
            ]
        ];
    }

    /**
     * Validate sale references
     */
    private function validateSaleReferences(): array
    {
        $errors = [];

        // Only BELOW and DISTRIBUTED sales should have sale_ids
        $salesWithSaleIds = $this->buildBaseQuery()
            ->whereNotNull('sale_ids')
            ->get();

        foreach ($salesWithSaleIds as $sale) {
            // Check if this sale type should have sale_ids
            if ($sale->ceiling === Ceiling::ABOVE->value) {
                $errors[] = [
                    'type' => 'unexpected_sale_ids',
                    'sale_id' => $sale->id,
                    'ceiling' => $sale->ceiling,
                    'sale_ids' => $sale->sale_ids,
                    'message' => 'ABOVE sale should not have sale_ids (original sales should have null sale_ids)'
                ];
            } elseif (in_array($sale->ceiling, [Ceiling::BELOW->value, Ceiling::DISTRIBUTED->value])) {
                // Validate sale_ids format for derived sales
                if ($sale->sale_ids) {
                    // Check if sale_ids format is valid (comma-separated numbers)
                    if (!preg_match('/^[\d,\s]+$/', $sale->sale_ids)) {
                        $errors[] = [
                            'type' => 'invalid_sale_ids_format',
                            'sale_id' => $sale->id,
                            'ceiling' => $sale->ceiling,
                            'sale_ids' => $sale->sale_ids,
                            'message' => 'Invalid sale_ids format (should be comma-separated numbers)'
                        ];
                    } else {
                        // Validate that all referenced IDs are numeric
                        $originalSaleIds = explode(',', $sale->sale_ids);
                        foreach ($originalSaleIds as $originalSaleId) {
                            $trimmedId = trim($originalSaleId);
                            if (!is_numeric($trimmedId) || $trimmedId <= 0) {
                                $errors[] = [
                                    'type' => 'invalid_sale_id_value',
                                    'sale_id' => $sale->id,
                                    'ceiling' => $sale->ceiling,
                                    'invalid_id' => $trimmedId,
                                    'sale_ids' => $sale->sale_ids,
                                    'message' => 'sale_ids contains invalid ID value (must be positive integer)'
                                ];
                            }
                        }
                    }
                }
            }
        }

        return [
            'errors' => $errors,
            'metrics' => [
                'invalid_references' => count($errors),
                'sales_with_sale_ids' => $salesWithSaleIds->count()
            ]
        ];
    }

    /**
     * Generate recommendations based on audit results
     */
    private function generateRecommendations(): void
    {
        $recommendations = [];

        // Analyze distribution integrity
        if (isset($this->auditResults['distribution_integrity']['integrity_validation']['errors'])) {
            $errors = $this->auditResults['distribution_integrity']['integrity_validation']['errors'];
            if (!empty($errors)) {
                $recommendations[] = [
                    'category' => 'distribution_integrity',
                    'priority' => 'high',
                    'title' => 'Fix Distribution Integrity Issues',
                    'description' => 'Critical distribution integrity errors detected that require immediate attention.',
                    'action_items' => [
                        'Review and fix quantity/value mismatches',
                        'Verify distribution calculations',
                        'Check for data corruption or processing errors'
                    ]
                ];
            }
        }

        // Analyze hierarchy issues
        if ($this->summaryStats['total_hierarchy_errors'] > 0) {
            $recommendations[] = [
                'category' => 'hierarchy',
                'priority' => 'medium',
                'title' => 'Resolve Hierarchy Issues',
                'description' => 'LineDivision hierarchy inconsistencies detected.',
                'action_items' => [
                    'Fix temporal relationship overlaps',
                    'Resolve orphaned divisions',
                    'Address circular references'
                ]
            ];
        }

        // Analyze split distribution issues
        if ($this->summaryStats['total_split_errors'] > 0) {
            $recommendations[] = [
                'category' => 'split_distribution',
                'priority' => 'medium',
                'title' => 'Fix Split Distribution Issues',
                'description' => '90/10 split distribution errors detected.',
                'action_items' => [
                    'Verify ratio calculations',
                    'Check distribution algorithm implementation',
                    'Ensure proper split percentages'
                ]
            ];
        }

        // Calculate overall health score
        $totalErrors = $this->summaryStats['total_distribution_errors'] +
                      $this->summaryStats['total_hierarchy_errors'] +
                      $this->summaryStats['total_split_errors'] +
                      $this->summaryStats['total_ceiling_errors'] +
                      $this->summaryStats['total_referential_errors'];

        $healthScore = $this->summaryStats['total_sales_analyzed'] > 0 ?
            max(0, (1 - ($totalErrors / $this->summaryStats['total_sales_analyzed'])) * 100) : 100;

        $this->summaryStats['distribution_integrity_score'] = round($healthScore, 2);

        $this->auditResults['recommendations'] = $recommendations;
    }

    /**
     * Calculate summary statistics
     */
    private function calculateSummaryStatistics(): void
    {
        $this->auditResults['summary'] = $this->summaryStats;
    }

    /**
     * Display audit results
     */
    private function displayResults(): void
    {
        if ($this->format === 'json') {
            $this->displayJsonResults();
        } elseif ($this->format === 'csv') {
            $this->displayCsvResults();
        } else {
            $this->displayTableResults();
        }
    }

    /**
     * Display results in table format
     */
    private function displayTableResults(): void
    {
        $this->newLine();
        $this->info('=== DISTRIBUTION AUDIT SUMMARY ===');

        // Summary statistics table
        $summaryData = [
            ['Metric', 'Value'],
            ['Total Sales Analyzed', number_format($this->summaryStats['total_sales_analyzed'])],
            ['Total Distributed Sales', number_format($this->summaryStats['total_distributed_sales'])],
            ['Total Above Sales', number_format($this->summaryStats['total_above_sales'])],
            ['Total Below Sales', number_format($this->summaryStats['total_below_sales'])],
            ['Distribution Errors', number_format($this->summaryStats['total_distribution_errors'])],
            ['Hierarchy Errors', number_format($this->summaryStats['total_hierarchy_errors'])],
            ['Split Errors', number_format($this->summaryStats['total_split_errors'])],
            ['Ceiling Errors', number_format($this->summaryStats['total_ceiling_errors'])],
            ['Referential Errors', number_format($this->summaryStats['total_referential_errors'])],
            ['Integrity Score', $this->summaryStats['distribution_integrity_score'] . '%']
        ];

        $this->table($summaryData[0], array_slice($summaryData, 1));

        if (!$this->summaryOnly) {
            $this->displayDetailedResults();
        }

        $this->displayRecommendations();
    }

    /**
     * Display detailed results
     */
    private function displayDetailedResults(): void
    {
        // Distribution integrity details
        if (isset($this->auditResults['distribution_integrity']['integrity_validation']['errors'])) {
            $errors = $this->auditResults['distribution_integrity']['integrity_validation']['errors'];
            if (!empty($errors)) {
                $this->newLine();
                $this->warn('=== DISTRIBUTION INTEGRITY ERRORS ===');
                foreach (array_slice($errors, 0, 10) as $error) {
                    $this->line("• {$error['message']}");
                }
                if (count($errors) > 10) {
                    $this->line("... and " . (count($errors) - 10) . " more errors");
                }
            }
        }

        // Hierarchy errors
        if ($this->summaryStats['total_hierarchy_errors'] > 0) {
            $this->newLine();
            $this->warn('=== HIERARCHY ERRORS ===');
            $this->line("Found {$this->summaryStats['total_hierarchy_errors']} hierarchy-related issues");
        }

        // Split distribution errors
        if ($this->summaryStats['total_split_errors'] > 0) {
            $this->newLine();
            $this->warn('=== SPLIT DISTRIBUTION ERRORS ===');
            $this->line("Found {$this->summaryStats['total_split_errors']} split distribution issues");
        }
    }

    /**
     * Display recommendations
     */
    private function displayRecommendations(): void
    {
        if (!empty($this->auditResults['recommendations'])) {
            $this->newLine();
            $this->info('=== RECOMMENDATIONS ===');

            foreach ($this->auditResults['recommendations'] as $recommendation) {
                $priority = strtoupper($recommendation['priority']);
                $this->line("🔸 [{$priority}] {$recommendation['title']}");
                $this->line("   {$recommendation['description']}");

                foreach ($recommendation['action_items'] as $item) {
                    $this->line("   - {$item}");
                }
                $this->newLine();
            }
        }
    }

    /**
     * Display results in JSON format
     */
    private function displayJsonResults(): void
    {
        $this->line(json_encode($this->auditResults, JSON_PRETTY_PRINT));
    }

    /**
     * Display results in CSV format
     */
    private function displayCsvResults(): void
    {
        $this->newLine();
        $this->info('=== DISTRIBUTION AUDIT REPORT (CSV FORMAT) ===');
        $this->line('Date Range: ' . $this->option('from-date') . ' to ' . $this->option('to-date'));
        $this->newLine();

        $csvData = $this->generateCsvData();

        // Display summary first
        $this->info('Summary Statistics:');
        $this->line('Total Sales Analyzed: ' . number_format($this->summaryStats['total_sales_analyzed']));
        $this->line('Distribution Integrity Score: ' . $this->summaryStats['distribution_integrity_score'] . '%');
        $this->line('Total Errors Found: ' . (count($csvData) - 1)); // Subtract header row
        $this->newLine();

        // Display CSV data in terminal
        $this->info('Detailed Error Report:');
        foreach ($csvData as $index => $row) {
            if ($index === 0) {
                // Header row - display with emphasis
                $this->warn('"' . implode('","', $row) . '"');
            } else {
                // Data rows
                $this->line('"' . implode('","', $row) . '"');
            }
        }

        if (count($csvData) === 1) {
            $this->info('🎉 No errors found in the audit!');
        }
    }

    /**
     * Export results to file
     */
    private function exportResults(): void
    {
        $timestamp = now()->format('Y-m-d_H-i-s');
        $filename = $this->exportFile . '_' . $timestamp;

        if ($this->format === 'json') {
            $this->exportToJson($filename);
        } elseif ($this->format === 'csv') {
            $this->exportToCsv($filename);
            // Also display CSV in terminal when exporting
            $this->newLine();
            $this->info('CSV data also displayed below:');
            $this->displayCsvResults();
        } else {
            $this->exportToJson($filename); // Default to JSON for table format
        }
    }

    /**
     * Export to JSON file
     */
    private function exportToJson(string $filename): void
    {
        $jsonContent = json_encode($this->auditResults, JSON_PRETTY_PRINT);
        $filepath = "distribution_audit_reports/{$filename}.json";

        Storage::disk('local')->put($filepath, $jsonContent);
        $this->info("✅ Audit report exported to: storage/app/{$filepath}");
    }

    /**
     * Generate CSV data structure
     */
    private function generateCsvData(): array
    {
        $csvData = [];

        // Header
        $csvData[] = [
            'Category',
            'Type',
            'Sale ID',
            'Message',
            'Severity',
            'Additional Data'
        ];

        // Add errors from all categories
        foreach ($this->auditResults as $category => $categoryData) {
            if (is_array($categoryData)) {
                $this->addErrorsToCsv($csvData, $category, $categoryData);
            }
        }

        return $csvData;
    }

    /**
     * Export to CSV file
     */
    private function exportToCsv(string $filename): void
    {
        $csvData = $this->generateCsvData();

        $csvContent = '';
        foreach ($csvData as $row) {
            $csvContent .= '"' . implode('","', $row) . '"' . "\n";
        }

        $filepath = "distribution_audit_reports/{$filename}.csv";
        Storage::disk('local')->put($filepath, $csvContent);
        $this->info("✅ Audit report exported to: storage/app/{$filepath}");
    }

    /**
     * Add errors to CSV data
     */
    private function addErrorsToCsv(array &$csvData, string $category, array $categoryData): void
    {
        if (isset($categoryData['errors'])) {
            foreach ($categoryData['errors'] as $error) {
                $csvData[] = [
                    $category,
                    $error['type'] ?? 'unknown',
                    $error['sale_id'] ?? '',
                    $error['message'] ?? '',
                    $error['severity'] ?? 'medium',
                    json_encode(array_diff_key($error, array_flip(['type', 'sale_id', 'message', 'severity'])))
                ];
            }
        }

        // Recursively check nested arrays
        foreach ($categoryData as $key => $value) {
            if (is_array($value) && $key !== 'errors') {
                $this->addErrorsToCsv($csvData, "{$category}.{$key}", $value);
            }
        }
    }

    /**
     * Log audit summary
     */
    private function logAuditSummary(): void
    {
        Log::info('Distribution audit report completed', [
            'date_range' => [
                'from' => $this->option('from-date'),
                'to' => $this->option('to-date')
            ],
            'distribution_type' => $this->option('distribution-type'),
            'summary_stats' => $this->summaryStats,
            'options' => [
                'tolerance' => $this->tolerance,
                'format' => $this->format,
                'export_file' => $this->exportFile,
                'include_flags' => [
                    'hierarchy' => $this->includeHierarchy,
                    'split_analysis' => $this->includeSplitAnalysis,
                    'ceiling_analysis' => $this->includeCeilingAnalysis,
                    'referential' => $this->includeReferential
                ]
            ]
        ]);
    }
}
