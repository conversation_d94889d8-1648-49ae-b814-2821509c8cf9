<?php

namespace App\Console\Commands;

use App\Sale;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ValidateSaleDetailsIntegrityCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'distribution:validate-sale-details
                            {--from-date= : Start date for validation (YYYY-MM-DD)}
                            {--to-date= : End date for validation (YYYY-MM-DD)}
                            {--distributor-ids= : Comma-separated distributor IDs to filter}
                            {--product-ids= : Comma-separated product IDs to filter}
                            {--tolerance=0.001 : Tolerance for rounding errors}
                            {--detailed : Show detailed breakdown of problematic sales}
                            {--export-csv= : Export results to CSV file}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Validate integrity of Sale distribution by checking that each Sale quantity matches the sum of its associated SaleDetail quantities';

    /**
     * Tolerance for rounding errors
     */
    private float $tolerance;

    /**
     * Whether to show detailed output
     */
    private bool $detailed;

    /**
     * CSV export filename
     */
    private ?string $exportCsv;

    /**
     * Validation results
     */
    private array $validationResults = [];

    /**
     * Summary statistics
     */
    private array $summaryStats = [];

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        try {
            // Initialize options
            $this->initializeOptions();

            // Validate input parameters
            if (!$this->validateInput()) {
                return Command::FAILURE;
            }

            // Print header
            $this->printHeader();

            // Perform validation
            $this->performValidation();

            // Display results
            $this->displayResults();

            // Export to CSV if requested
            if ($this->exportCsv) {
                $this->exportToCsv();
            }

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error("Error during validation: " . $e->getMessage());
            $this->error("Stack trace: " . $e->getTraceAsString());
            return Command::FAILURE;
        }
    }

    /**
     * Initialize command options
     */
    private function initializeOptions(): void
    {
        $this->tolerance = (float) $this->option('tolerance');
        $this->detailed = $this->option('detailed');
        $this->exportCsv = $this->option('export-csv');
    }

    /**
     * Validate input parameters
     */
    private function validateInput(): bool
    {
        $fromDate = $this->option('from-date');
        $toDate = $this->option('to-date');

        if (!$fromDate || !$toDate) {
            $this->error('Both --from-date and --to-date are required');
            $this->line('');
            $this->line('Example: php artisan distribution:validate-sale-details --from-date=2024-01-01 --to-date=2024-01-31');
            return false;
        }

        try {
            $from = Carbon::createFromFormat('Y-m-d', $fromDate);
            $to = Carbon::createFromFormat('Y-m-d', $toDate);

            if ($from->gt($to)) {
                $this->error('from-date cannot be after to-date');
                return false;
            }
        } catch (\Exception) {
            $this->error('Invalid date format. Use YYYY-MM-DD');
            return false;
        }

        return true;
    }

    /**
     * Print header information
     */
    private function printHeader(): void
    {
        $this->info("=== SALE DETAILS INTEGRITY VALIDATION ===");
        $this->line("Date Range: {$this->option('from-date')} to {$this->option('to-date')}");
        $this->line("Tolerance: {$this->tolerance}");
        $this->line("Detailed Output: " . ($this->detailed ? "YES" : "NO"));

        if ($this->option('distributor-ids')) {
            $this->line("Distributor IDs: {$this->option('distributor-ids')}");
        }

        if ($this->option('product-ids')) {
            $this->line("Product IDs: {$this->option('product-ids')}");
        }

        if ($this->exportCsv) {
            $this->line("Export CSV: {$this->exportCsv}");
        }

        $this->line("");
    }

    /**
     * Perform the main validation logic
     */
    private function performValidation(): void
    {
        $this->info("Starting sale details integrity validation...");
        $this->line("");

        // Get sales with their detail sums
        $salesData = $this->getSalesWithDetailSums();

        $this->info("Processing " . $salesData->count() . " sales records...");
        $this->line("");

        $healthySales = 0;
        $problematicSales = 0;
        $salesWithNoDetails = 0;

        foreach ($salesData as $saleData) {
            $saleQuantity = (float) $saleData->sale_quantity;
            $detailsSum = (float) ($saleData->details_sum ?? 0);
            $difference = abs($saleQuantity - $detailsSum);

            $result = [
                'sale_id' => $saleData->sale_id,
                'sale_quantity' => $saleQuantity,
                'details_sum' => $detailsSum,
                'difference' => $difference,
                'distributor_id' => $saleData->distributor_id,
                'product_id' => $saleData->product_id,
                'date' => $saleData->date,
                'details_count' => $saleData->details_count ?? 0,
            ];

            if ($detailsSum == 0 && $saleQuantity > 0) {
                $result['status'] = 'NO_DETAILS';
                $salesWithNoDetails++;
            } elseif ($difference <= $this->tolerance) {
                $result['status'] = 'HEALTHY';
                $healthySales++;
            } else {
                $result['status'] = 'PROBLEMATIC';
                $problematicSales++;
            }

            $this->validationResults[] = $result;
        }

        // Store summary statistics
        $this->summaryStats = [
            'total_sales' => $salesData->count(),
            'healthy_sales' => $healthySales,
            'problematic_sales' => $problematicSales,
            'sales_with_no_details' => $salesWithNoDetails,
            'tolerance_used' => $this->tolerance,
        ];
    }

    /**
     * Get sales with their detail sums using Eloquent ORM
     */
    private function getSalesWithDetailSums(): Collection
    {
        $query = Sale::select([
            'sales.id as sale_id',
            'sales.quantity as sale_quantity',
            'sales.distributor_id',
            'sales.product_id',
            'sales.date',
            DB::raw('COALESCE(SUM(crm_sales_details.quantity), 0) as details_sum'),
            DB::raw('COUNT(crm_sales_details.id) as details_count')
        ])
        ->leftJoin('sales_details', 'sales.id', '=', 'sales_details.sale_id')
        ->whereBetween('sales.date', [$this->option('from-date'), $this->option('to-date')])
        ->groupBy([
            'sales.id',
            'sales.quantity',
            'sales.distributor_id',
            'sales.product_id',
            'sales.date'
        ]);

        // Apply filters if provided
        if ($this->option('distributor-ids')) {
            $distributorIds = explode(',', $this->option('distributor-ids'));
            $query->whereIn('sales.distributor_id', $distributorIds);
        }

        if ($this->option('product-ids')) {
            $productIds = explode(',', $this->option('product-ids'));
            $query->whereIn('sales.product_id', $productIds);
        }

        return $query->get();
    }

    /**
     * Display validation results
     */
    private function displayResults(): void
    {
        $summary = $this->summaryStats;

        $this->info("=== VALIDATION SUMMARY ===");
        $this->line("Total Sales Checked: {$summary['total_sales']}");
        $this->line("Healthy Sales: {$summary['healthy_sales']}");
        $this->line("Problematic Sales: {$summary['problematic_sales']}");
        $this->line("Sales with No Details: {$summary['sales_with_no_details']}");
        $this->line("Tolerance Used: {$summary['tolerance_used']}");
        $this->line("");

        // Display problematic sales table
        if ($summary['problematic_sales'] > 0) {
            $this->displayProblematicSales();
        }

        // Display sales with no details
        if ($summary['sales_with_no_details'] > 0) {
            $this->displaySalesWithNoDetails();
        }

        // Display detailed breakdown if requested
        if ($this->detailed && ($summary['problematic_sales'] > 0 || $summary['sales_with_no_details'] > 0)) {
            $this->displayDetailedBreakdown();
        }

        // Display final status
        $this->displayFinalStatus($summary);
    }

    /**
     * Display problematic sales in table format
     */
    private function displayProblematicSales(): void
    {
        $this->error("=== PROBLEMATIC SALES (Quantity Mismatch) ===");

        $problematicSales = array_filter($this->validationResults, function($result) {
            return is_array($result) && $result['status'] === 'PROBLEMATIC';
        });

        if (empty($problematicSales)) {
            return;
        }

        $headers = ['Sale ID', 'Sale Qty', 'Details Sum', 'Difference', 'Distributor', 'Product', 'Date'];
        $rows = [];

        foreach ($problematicSales as $sale) {
            $rows[] = [
                $sale['sale_id'],
                number_format($sale['sale_quantity'], 5),
                number_format($sale['details_sum'], 5),
                number_format($sale['difference'], 5),
                $sale['distributor_id'],
                $sale['product_id'],
                $sale['date'],
            ];
        }

        $this->table($headers, $rows);
        $this->line("");
    }

    /**
     * Display sales with no details
     */
    private function displaySalesWithNoDetails(): void
    {
        $this->warn("=== SALES WITH NO DETAILS ===");

        $salesWithNoDetails = array_filter($this->validationResults, function($result) {
            return is_array($result) && $result['status'] === 'NO_DETAILS';
        });

        if (empty($salesWithNoDetails)) {
            return;
        }

        $headers = ['Sale ID', 'Sale Qty', 'Distributor', 'Product', 'Date'];
        $rows = [];

        foreach ($salesWithNoDetails as $sale) {
            $rows[] = [
                $sale['sale_id'],
                number_format($sale['sale_quantity'], 5),
                $sale['distributor_id'],
                $sale['product_id'],
                $sale['date'],
            ];
        }

        $this->table($headers, $rows);
        $this->line("");
    }

    /**
     * Display detailed breakdown
     */
    private function displayDetailedBreakdown(): void
    {
        $this->info("=== DETAILED BREAKDOWN ===");

        // Group by distributor
        $this->displayBreakdownByDistributor();

        // Group by product
        $this->displayBreakdownByProduct();

        // Group by date
        $this->displayBreakdownByDate();
    }

    /**
     * Display breakdown by distributor
     */
    private function displayBreakdownByDistributor(): void
    {
        $this->line("--- By Distributor ---");

        $distributorStats = [];
        foreach ($this->validationResults as $result) {
            if (!is_array($result) || $result['status'] === 'HEALTHY') {
                continue;
            }

            $distributorId = $result['distributor_id'];
            if (!isset($distributorStats[$distributorId])) {
                $distributorStats[$distributorId] = [
                    'problematic' => 0,
                    'no_details' => 0,
                    'total_difference' => 0,
                ];
            }

            if ($result['status'] === 'PROBLEMATIC') {
                $distributorStats[$distributorId]['problematic']++;
                $distributorStats[$distributorId]['total_difference'] += $result['difference'];
            } elseif ($result['status'] === 'NO_DETAILS') {
                $distributorStats[$distributorId]['no_details']++;
            }
        }

        foreach ($distributorStats as $distributorId => $stats) {
            $this->line("Distributor {$distributorId}: Problematic={$stats['problematic']}, No Details={$stats['no_details']}, Total Diff=" . number_format($stats['total_difference'], 5));
        }
        $this->line("");
    }

    /**
     * Display breakdown by product
     */
    private function displayBreakdownByProduct(): void
    {
        $this->line("--- By Product ---");

        $productStats = [];
        foreach ($this->validationResults as $result) {
            if (!is_array($result) || $result['status'] === 'HEALTHY') {
                continue;
            }

            $productId = $result['product_id'];
            if (!isset($productStats[$productId])) {
                $productStats[$productId] = [
                    'problematic' => 0,
                    'no_details' => 0,
                    'total_difference' => 0,
                ];
            }

            if ($result['status'] === 'PROBLEMATIC') {
                $productStats[$productId]['problematic']++;
                $productStats[$productId]['total_difference'] += $result['difference'];
            } elseif ($result['status'] === 'NO_DETAILS') {
                $productStats[$productId]['no_details']++;
            }
        }

        foreach ($productStats as $productId => $stats) {
            $this->line("Product {$productId}: Problematic={$stats['problematic']}, No Details={$stats['no_details']}, Total Diff=" . number_format($stats['total_difference'], 5));
        }
        $this->line("");
    }

    /**
     * Display breakdown by date
     */
    private function displayBreakdownByDate(): void
    {
        $this->line("--- By Date ---");

        $dateStats = [];
        foreach ($this->validationResults as $result) {
            if (!is_array($result) || $result['status'] === 'HEALTHY') {
                continue;
            }

            $date = $result['date'];
            if (!isset($dateStats[$date])) {
                $dateStats[$date] = [
                    'problematic' => 0,
                    'no_details' => 0,
                    'total_difference' => 0,
                ];
            }

            if ($result['status'] === 'PROBLEMATIC') {
                $dateStats[$date]['problematic']++;
                $dateStats[$date]['total_difference'] += $result['difference'];
            } elseif ($result['status'] === 'NO_DETAILS') {
                $dateStats[$date]['no_details']++;
            }
        }

        ksort($dateStats); // Sort by date
        foreach ($dateStats as $date => $stats) {
            $this->line("Date {$date}: Problematic={$stats['problematic']}, No Details={$stats['no_details']}, Total Diff=" . number_format($stats['total_difference'], 5));
        }
        $this->line("");
    }

    /**
     * Display final validation status
     */
    private function displayFinalStatus(array $summary): void
    {
        $totalIssues = $summary['problematic_sales'] + $summary['sales_with_no_details'];

        if ($totalIssues === 0) {
            $this->info("✅ VALIDATION PASSED: All sales have matching detail quantities within tolerance.");
        } else {
            $this->error("❌ VALIDATION FAILED: Found {$totalIssues} sales with integrity issues.");

            if ($summary['problematic_sales'] > 0) {
                $this->error("   - {$summary['problematic_sales']} sales with quantity mismatches");
            }

            if ($summary['sales_with_no_details'] > 0) {
                $this->error("   - {$summary['sales_with_no_details']} sales with no detail records");
            }
        }

        $this->line("");
        $this->info("Validation completed at " . now()->format('Y-m-d H:i:s'));
    }

    /**
     * Export results to CSV file
     */
    private function exportToCsv(): void
    {
        try {
            $filename = $this->exportCsv;
            if (!str_ends_with($filename, '.csv')) {
                $filename .= '.csv';
            }

            $filepath = storage_path("app/exports/{$filename}");

            // Ensure directory exists
            $directory = dirname($filepath);
            if (!is_dir($directory)) {
                mkdir($directory, 0755, true);
            }

            $file = fopen($filepath, 'w');

            // Write CSV headers
            fputcsv($file, [
                'Sale ID',
                'Status',
                'Sale Quantity',
                'Details Sum',
                'Difference',
                'Details Count',
                'Distributor ID',
                'Product ID',
                'Date'
            ]);

            // Write data rows
            foreach ($this->validationResults as $result) {
                if (!is_array($result) || !isset($result['sale_id'])) {
                    continue;
                }

                fputcsv($file, [
                    $result['sale_id'],
                    $result['status'],
                    $result['sale_quantity'],
                    $result['details_sum'],
                    $result['difference'],
                    $result['details_count'],
                    $result['distributor_id'],
                    $result['product_id'],
                    $result['date']
                ]);
            }

            fclose($file);

            $this->info("Results exported to: {$filepath}");

        } catch (\Exception $e) {
            $this->error("Failed to export CSV: " . $e->getMessage());
        }
    }
}
