<?php

namespace App\Console\Commands;

use App\Sale;
use App\SaleDetail;
use App\Services\Enums\Ceiling;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Collection;

class ScanDistributionErrorsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'distribution:scan-errors
                            {--from-date= : Start date for scanning (YYYY-MM-DD)}
                            {--to-date= : End date for scanning (YYYY-MM-DD)}
                            {--distribution-type= : Distribution type (1=Private Pharmacy, 2=Store, 3=Local Chain)}
                            {--product-ids= : Comma-separated product IDs to filter}
                            {--distributor-ids= : Comma-separated distributor IDs to filter}
                            {--tolerance=0.001 : Tolerance for rounding errors}
                            {--detailed : Show detailed breakdown by error types}
                            {--export-csv= : Export results to CSV file}
                            {--fix-errors : Attempt to fix detected errors (use with caution)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Scan the database to identify sales records with incorrect distribution patterns and inconsistencies';

    /**
     * Tolerance for rounding errors
     */
    private float $tolerance;

    /**
     * Whether to show detailed output
     */
    private bool $detailed;

    /**
     * CSV export filename
     */
    private ?string $exportCsv;

    /**
     * Whether to attempt fixing errors
     */
    private bool $fixErrors;

    /**
     * Collection to store detected errors
     */
    private Collection $detectedErrors;

    /**
     * Error statistics
     */
    private array $errorStats = [
        'distributed_without_records' => 0,
        'mismatched_totals' => 0,
        'incorrect_ceiling_transitions' => 0,
        'orphaned_distributions' => 0,
        'details_misalignment' => 0,
        'split_validation_errors' => 0,
        'negative_quantities' => 0,
        'null_critical_fields' => 0,
        'invalid_sale_ids' => 0,
        'total_errors' => 0
    ];

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        try {
            // Initialize options
            $this->initializeOptions();

            // Validate input parameters
            if (!$this->validateInput()) {
                return Command::FAILURE;
            }

            $this->printHeader();

            // Initialize error collection
            $this->detectedErrors = collect();

            // Run all error detection checks
            $this->runErrorDetectionChecks();

            // Display results
            $this->displayResults();

            // Export to CSV if requested
            if ($this->exportCsv) {
                $this->exportToCsv();
            }

            // Attempt to fix errors if requested
            if ($this->fixErrors && $this->detectedErrors->isNotEmpty()) {
                $this->attemptErrorFixes();
            }

            // Log summary
            $this->logSummary();

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('❌ ERROR: ' . $e->getMessage());
            Log::error('Distribution error scanning failed', [
                'error' => $e->getMessage(),
                'options' => $this->options()
            ]);
            return Command::FAILURE;
        }
    }

    /**
     * Initialize command options
     */
    private function initializeOptions(): void
    {
        $this->tolerance = (float) $this->option('tolerance');
        $this->detailed = $this->option('detailed');
        $this->exportCsv = $this->option('export-csv');
        $this->fixErrors = $this->option('fix-errors');
    }

    /**
     * Validate input parameters
     */
    private function validateInput(): bool
    {
        $fromDate = $this->option('from-date');
        $toDate = $this->option('to-date');

        if (!$fromDate || !$toDate) {
            $this->error('Both --from-date and --to-date are required');
            $this->line('');
            $this->line('Example: php artisan distribution:scan-errors --from-date=2024-01-01 --to-date=2024-01-31');
            return false;
        }

        try {
            $from = Carbon::createFromFormat('Y-m-d', $fromDate);
            $to = Carbon::createFromFormat('Y-m-d', $toDate);

            if ($from->gt($to)) {
                $this->error('from-date cannot be after to-date');
                return false;
            }
        } catch (\Exception $e) {
            $this->error('Invalid date format. Use YYYY-MM-DD');
            return false;
        }

        return true;
    }

    /**
     * Print command header
     */
    private function printHeader(): void
    {
        $this->info('=== DISTRIBUTION ERROR SCANNER ===');
        $this->line('Scanning for distribution inconsistencies and errors...');
        $this->line('Date Range: ' . $this->option('from-date') . ' to ' . $this->option('to-date'));
        
        if ($this->option('distribution-type')) {
            $typeName = $this->getDistributionTypeName($this->option('distribution-type'));
            $this->line('Distribution Type: ' . $typeName);
        }
        
        $this->line('Tolerance: ' . $this->tolerance);
        $this->newLine();
    }

    /**
     * Get distribution type name
     */
    private function getDistributionTypeName(string $type): string
    {
        return match($type) {
            '1' => 'Private Pharmacy',
            '2' => 'Store',
            '3' => 'Local Chain',
            default => 'Unknown'
        };
    }

    /**
     * Build base query for sales filtering
     */
    private function buildBaseQuery()
    {
        $query = Sale::whereBetween('date', [
            $this->option('from-date'),
            $this->option('to-date')
        ]);

        // Apply distribution type filter if specified
        if ($this->option('distribution-type')) {
            // Add distribution type filtering logic based on your mapping structure
            // This would need to be adapted based on how distribution types are stored
        }

        // Apply product filter
        if ($this->option('product-ids')) {
            $productIds = explode(',', $this->option('product-ids'));
            $query->whereIn('product_id', $productIds);
        }

        // Apply distributor filter
        if ($this->option('distributor-ids')) {
            $distributorIds = explode(',', $this->option('distributor-ids'));
            $query->whereIn('distributor_id', $distributorIds);
        }

        return $query;
    }

    /**
     * Run all error detection checks
     */
    private function runErrorDetectionChecks(): void
    {
        $this->info('🔍 Running error detection checks...');
        
        // Check 1: Sales marked as distributed but missing distribution records
        $this->checkDistributedWithoutRecords();
        
        // Check 2: Distribution totals that don't match original sale amounts
        $this->checkMismatchedTotals();
        
        // Check 3: Incorrect ceiling status transitions
        $this->checkIncorrectCeilingTransitions();
        
        // Check 4: Orphaned distribution records
        $this->checkOrphanedDistributions();
        
        // Check 5: Sales details misalignment
        $this->checkDetailsMisalignment();
        
        // Check 6: 90/10 split validation errors (for Store strategy)
        if ($this->option('distribution-type') == '2') {
            $this->checkSplitValidationErrors();
        }
        
        // Check 7: Negative quantities
        $this->checkNegativeQuantities();
        
        // Check 8: Null critical fields
        $this->checkNullCriticalFields();
        
        // Check 9: Invalid sale_ids references
        $this->checkInvalidSaleIds();
        
        $this->calculateTotalErrors();
    }

    /**
     * Check for sales marked as distributed but missing distribution records
     */
    private function checkDistributedWithoutRecords(): void
    {
        $this->line('Checking for distributed sales without records...');
        
        $distributedSales = $this->buildBaseQuery()
            ->where('ceiling', Ceiling::DISTRIBUTED->value)
            ->whereNotNull('sale_ids')
            ->get();

        foreach ($distributedSales as $sale) {
            $details = SaleDetail::where('sale_id', $sale->id)->count();
            
            if ($details === 0) {
                $this->addError('distributed_without_records', [
                    'sale_id' => $sale->id,
                    'distributor_id' => $sale->distributor_id,
                    'product_id' => $sale->product_id,
                    'date' => $sale->date,
                    'quantity' => $sale->quantity,
                    'value' => $sale->value,
                    'description' => 'Sale marked as distributed but has no distribution details'
                ]);
                $this->errorStats['distributed_without_records']++;
            }
        }
        
        $this->info("✓ Found {$this->errorStats['distributed_without_records']} distributed sales without records");
    }

    /**
     * Check for distribution totals that don't match original sale amounts
     */
    private function checkMismatchedTotals(): void
    {
        $this->line('Checking for mismatched distribution totals...');

        $distributedSales = $this->buildBaseQuery()
            ->where('ceiling', Ceiling::DISTRIBUTED->value)
            ->whereNotNull('sale_ids')
            ->get();

        foreach ($distributedSales as $sale) {
            $details = SaleDetail::where('sale_id', $sale->id)->get();

            if ($details->isNotEmpty()) {
                $totalDetailQuantity = $details->sum('quantity');
                $totalDetailValue = $details->sum('value');
                $totalDetailBonus = $details->sum('bonus');

                $quantityDiff = abs($sale->quantity - $totalDetailQuantity);
                $valueDiff = abs($sale->value - $totalDetailValue);
                $bonusDiff = abs($sale->bonus - $totalDetailBonus);

                if ($quantityDiff > $this->tolerance || $valueDiff > $this->tolerance || $bonusDiff > $this->tolerance) {
                    $this->addError('mismatched_totals', [
                        'sale_id' => $sale->id,
                        'distributor_id' => $sale->distributor_id,
                        'product_id' => $sale->product_id,
                        'date' => $sale->date,
                        'expected_quantity' => $sale->quantity,
                        'actual_quantity' => $totalDetailQuantity,
                        'quantity_diff' => $quantityDiff,
                        'expected_value' => $sale->value,
                        'actual_value' => $totalDetailValue,
                        'value_diff' => $valueDiff,
                        'expected_bonus' => $sale->bonus,
                        'actual_bonus' => $totalDetailBonus,
                        'bonus_diff' => $bonusDiff,
                        'description' => 'Distribution totals do not match original sale amounts'
                    ]);
                    $this->errorStats['mismatched_totals']++;
                }
            }
        }

        $this->info("✓ Found {$this->errorStats['mismatched_totals']} sales with mismatched totals");
    }

    /**
     * Check for incorrect ceiling status transitions
     */
    private function checkIncorrectCeilingTransitions(): void
    {
        $this->line('Checking for incorrect ceiling status transitions...');

        // Check for sales with DISTRIBUTED status but no sale_ids
        $invalidDistributed = $this->buildBaseQuery()
            ->where('ceiling', Ceiling::DISTRIBUTED->value)
            ->whereNull('sale_ids')
            ->get();

        foreach ($invalidDistributed as $sale) {
            $this->addError('incorrect_ceiling_transitions', [
                'sale_id' => $sale->id,
                'distributor_id' => $sale->distributor_id,
                'product_id' => $sale->product_id,
                'date' => $sale->date,
                'ceiling' => $sale->ceiling,
                'sale_ids' => $sale->sale_ids,
                'description' => 'Sale marked as DISTRIBUTED but has no sale_ids reference'
            ]);
            $this->errorStats['incorrect_ceiling_transitions']++;
        }

        // Check for sales with sale_ids but not DISTRIBUTED status
        $invalidSaleIds = $this->buildBaseQuery()
            ->where('ceiling', '!=', Ceiling::DISTRIBUTED->value)
            ->whereNotNull('sale_ids')
            ->get();

        foreach ($invalidSaleIds as $sale) {
            $this->addError('incorrect_ceiling_transitions', [
                'sale_id' => $sale->id,
                'distributor_id' => $sale->distributor_id,
                'product_id' => $sale->product_id,
                'date' => $sale->date,
                'ceiling' => $sale->ceiling,
                'sale_ids' => $sale->sale_ids,
                'description' => 'Sale has sale_ids but is not marked as DISTRIBUTED'
            ]);
            $this->errorStats['incorrect_ceiling_transitions']++;
        }

        $this->info("✓ Found {$this->errorStats['incorrect_ceiling_transitions']} incorrect ceiling transitions");
    }

    /**
     * Check for orphaned distribution records
     */
    private function checkOrphanedDistributions(): void
    {
        $this->line('Checking for orphaned distribution records...');

        $distributedSales = $this->buildBaseQuery()
            ->where('ceiling', Ceiling::DISTRIBUTED->value)
            ->whereNotNull('sale_ids')
            ->get();

        foreach ($distributedSales as $sale) {
            if ($sale->sale_ids) {
                $originalSaleIds = explode(',', $sale->sale_ids);

                foreach ($originalSaleIds as $originalSaleId) {
                    $originalSale = Sale::find(trim($originalSaleId));

                    if (!$originalSale) {
                        $this->addError('orphaned_distributions', [
                            'sale_id' => $sale->id,
                            'distributor_id' => $sale->distributor_id,
                            'product_id' => $sale->product_id,
                            'date' => $sale->date,
                            'missing_original_sale_id' => trim($originalSaleId),
                            'sale_ids' => $sale->sale_ids,
                            'description' => 'Distribution references non-existent original sale'
                        ]);
                        $this->errorStats['orphaned_distributions']++;
                    } elseif ($originalSale->ceiling !== Ceiling::ABOVE->value) {
                        $this->addError('orphaned_distributions', [
                            'sale_id' => $sale->id,
                            'distributor_id' => $sale->distributor_id,
                            'product_id' => $sale->product_id,
                            'date' => $sale->date,
                            'original_sale_id' => $originalSale->id,
                            'original_ceiling' => $originalSale->ceiling,
                            'description' => 'Distribution references original sale with incorrect ceiling status'
                        ]);
                        $this->errorStats['orphaned_distributions']++;
                    }
                }
            }
        }

        $this->info("✓ Found {$this->errorStats['orphaned_distributions']} orphaned distribution records");
    }

    /**
     * Check for sales details misalignment
     */
    private function checkDetailsMisalignment(): void
    {
        $this->line('Checking for sales details misalignment...');

        $salesWithDetails = $this->buildBaseQuery()
            ->whereHas('details')
            ->get();

        foreach ($salesWithDetails as $sale) {
            $details = SaleDetail::where('sale_id', $sale->id)->get();

            // Check if sale should have details based on ceiling status
            if ($sale->ceiling === Ceiling::BELOW->value && $details->isNotEmpty()) {
                $this->addError('details_misalignment', [
                    'sale_id' => $sale->id,
                    'distributor_id' => $sale->distributor_id,
                    'product_id' => $sale->product_id,
                    'date' => $sale->date,
                    'ceiling' => $sale->ceiling,
                    'details_count' => $details->count(),
                    'description' => 'BELOW sale should not have distribution details'
                ]);
                $this->errorStats['details_misalignment']++;
            }

            // Check for details with invalid ratios
            foreach ($details as $detail) {
                if ($detail->ratio < 0 || $detail->ratio > 1) {
                    $this->addError('details_misalignment', [
                        'sale_id' => $sale->id,
                        'detail_id' => $detail->id,
                        'distributor_id' => $sale->distributor_id,
                        'product_id' => $sale->product_id,
                        'date' => $sale->date,
                        'ratio' => $detail->ratio,
                        'description' => 'Sale detail has invalid ratio (should be between 0 and 1)'
                    ]);
                    $this->errorStats['details_misalignment']++;
                }
            }
        }

        $this->info("✓ Found {$this->errorStats['details_misalignment']} sales details misalignments");
    }

    /**
     * Check for 90/10 split validation errors (Store strategy)
     */
    private function checkSplitValidationErrors(): void
    {
        $this->line('Checking for 90/10 split validation errors...');

        $distributedSales = $this->buildBaseQuery()
            ->where('ceiling', Ceiling::DISTRIBUTED->value)
            ->whereNotNull('sale_ids')
            ->get();

        foreach ($distributedSales as $sale) {
            $details = SaleDetail::where('sale_id', $sale->id)->get();

            if ($details->isNotEmpty()) {
                // Check if total ratio is close to 1.0
                $totalRatio = $details->sum('ratio');

                if (abs($totalRatio - 1.0) > $this->tolerance) {
                    $this->addError('split_validation_errors', [
                        'sale_id' => $sale->id,
                        'distributor_id' => $sale->distributor_id,
                        'product_id' => $sale->product_id,
                        'date' => $sale->date,
                        'expected_ratio' => 1.0,
                        'actual_ratio' => $totalRatio,
                        'ratio_diff' => abs($totalRatio - 1.0),
                        'description' => '90/10 split total ratio deviates from 1.0'
                    ]);
                    $this->errorStats['split_validation_errors']++;
                }
            }
        }

        $this->info("✓ Found {$this->errorStats['split_validation_errors']} 90/10 split validation errors");
    }

    /**
     * Check for negative quantities
     */
    private function checkNegativeQuantities(): void
    {
        $this->line('Checking for negative quantities...');

        $negativeSales = $this->buildBaseQuery()
            ->where('quantity', '<', 0)
            ->get();

        foreach ($negativeSales as $sale) {
            $this->addError('negative_quantities', [
                'sale_id' => $sale->id,
                'distributor_id' => $sale->distributor_id,
                'product_id' => $sale->product_id,
                'date' => $sale->date,
                'quantity' => $sale->quantity,
                'description' => 'Sale has negative quantity'
            ]);
            $this->errorStats['negative_quantities']++;
        }

        $this->info("✓ Found {$this->errorStats['negative_quantities']} sales with negative quantities");
    }

    /**
     * Check for null critical fields
     */
    private function checkNullCriticalFields(): void
    {
        $this->line('Checking for null critical fields...');

        $salesWithNulls = $this->buildBaseQuery()
            ->where(function($query) {
                $query->whereNull('quantity')
                      ->orWhereNull('value')
                      ->orWhereNull('bonus')
                      ->orWhereNull('distributor_id')
                      ->orWhereNull('product_id');
            })
            ->get();

        foreach ($salesWithNulls as $sale) {
            $nullFields = [];
            if (is_null($sale->quantity)) $nullFields[] = 'quantity';
            if (is_null($sale->value)) $nullFields[] = 'value';
            if (is_null($sale->bonus)) $nullFields[] = 'bonus';
            if (is_null($sale->distributor_id)) $nullFields[] = 'distributor_id';
            if (is_null($sale->product_id)) $nullFields[] = 'product_id';

            $this->addError('null_critical_fields', [
                'sale_id' => $sale->id,
                'distributor_id' => $sale->distributor_id,
                'product_id' => $sale->product_id,
                'date' => $sale->date,
                'null_fields' => implode(', ', $nullFields),
                'description' => 'Sale has null values in critical fields: ' . implode(', ', $nullFields)
            ]);
            $this->errorStats['null_critical_fields']++;
        }

        $this->info("✓ Found {$this->errorStats['null_critical_fields']} sales with null critical fields");
    }

    /**
     * Check for invalid sale_ids references
     */
    private function checkInvalidSaleIds(): void
    {
        $this->line('Checking for invalid sale_ids references...');

        $salesWithSaleIds = $this->buildBaseQuery()
            ->whereNotNull('sale_ids')
            ->get();

        foreach ($salesWithSaleIds as $sale) {
            if ($sale->sale_ids) {
                // Check if sale_ids format is valid (comma-separated numbers)
                if (!preg_match('/^[\d,\s]+$/', $sale->sale_ids)) {
                    $this->addError('invalid_sale_ids', [
                        'sale_id' => $sale->id,
                        'distributor_id' => $sale->distributor_id,
                        'product_id' => $sale->product_id,
                        'date' => $sale->date,
                        'sale_ids' => $sale->sale_ids,
                        'description' => 'Invalid sale_ids format (should be comma-separated numbers)'
                    ]);
                    $this->errorStats['invalid_sale_ids']++;
                }
            }
        }

        $this->info("✓ Found {$this->errorStats['invalid_sale_ids']} sales with invalid sale_ids");
    }

    /**
     * Calculate total errors
     */
    private function calculateTotalErrors(): void
    {
        $this->errorStats['total_errors'] = array_sum(array_filter($this->errorStats, function($key) {
            return $key !== 'total_errors';
        }, ARRAY_FILTER_USE_KEY));
    }

    /**
     * Add error to collection
     */
    private function addError(string $type, array $data): void
    {
        $this->detectedErrors->push([
            'type' => $type,
            'data' => $data,
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * Display scan results
     */
    private function displayResults(): void
    {
        $this->newLine();
        $this->info('=== SCAN RESULTS ===');

        if ($this->errorStats['total_errors'] === 0) {
            $this->info('🎉 No distribution errors found!');
            return;
        }

        $this->warn("Found {$this->errorStats['total_errors']} total errors:");

        foreach ($this->errorStats as $type => $count) {
            if ($type !== 'total_errors' && $count > 0) {
                $this->line("  • " . ucwords(str_replace('_', ' ', $type)) . ": {$count}");
            }
        }

        if ($this->detailed) {
            $this->displayDetailedErrors();
        }
    }

    /**
     * Display detailed error breakdown
     */
    private function displayDetailedErrors(): void
    {
        $this->newLine();
        $this->info('=== DETAILED ERROR BREAKDOWN ===');

        $errorsByType = $this->detectedErrors->groupBy('type');

        foreach ($errorsByType as $type => $errors) {
            $this->newLine();
            $this->warn("--- " . ucwords(str_replace('_', ' ', $type)) . " ({$errors->count()}) ---");

            foreach ($errors->take(10) as $error) { // Show first 10 errors of each type
                $data = $error['data'];
                $this->line("Sale ID: {$data['sale_id']} | {$data['description']}");

                if (isset($data['expected_quantity']) && isset($data['actual_quantity'])) {
                    $this->line("  Expected: {$data['expected_quantity']}, Actual: {$data['actual_quantity']}, Diff: {$data['quantity_diff']}");
                }
            }

            if ($errors->count() > 10) {
                $remaining = $errors->count() - 10;
                $this->line("  ... and {$remaining} more errors of this type");
            }
        }
    }

    /**
     * Export results to CSV
     */
    private function exportToCsv(): void
    {
        if ($this->detectedErrors->isEmpty()) {
            $this->info('No errors to export.');
            return;
        }

        $filename = $this->exportCsv . '_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $csvData = [];
        $csvData[] = [
            'Error Type',
            'Sale ID',
            'Distributor ID',
            'Product ID',
            'Date',
            'Description',
            'Expected Quantity',
            'Actual Quantity',
            'Expected Value',
            'Actual Value',
            'Additional Info',
            'Timestamp'
        ];

        foreach ($this->detectedErrors as $error) {
            $data = $error['data'];
            $csvData[] = [
                $error['type'],
                $data['sale_id'] ?? '',
                $data['distributor_id'] ?? '',
                $data['product_id'] ?? '',
                $data['date'] ?? '',
                $data['description'] ?? '',
                $data['expected_quantity'] ?? '',
                $data['actual_quantity'] ?? '',
                $data['expected_value'] ?? '',
                $data['actual_value'] ?? '',
                json_encode(array_diff_key($data, array_flip([
                    'sale_id', 'distributor_id', 'product_id', 'date', 'description',
                    'expected_quantity', 'actual_quantity', 'expected_value', 'actual_value'
                ]))),
                $error['timestamp']
            ];
        }

        $csvContent = '';
        foreach ($csvData as $row) {
            $csvContent .= '"' . implode('","', $row) . '"' . "\n";
        }

        Storage::disk('local')->put("distribution_errors/{$filename}", $csvContent);

        $this->info("✅ Results exported to: storage/app/distribution_errors/{$filename}");
    }

    /**
     * Attempt to fix detected errors
     */
    private function attemptErrorFixes(): void
    {
        $this->newLine();
        $this->warn('⚠️  ERROR FIXING IS EXPERIMENTAL - USE WITH CAUTION');

        if (!$this->confirm('Do you want to proceed with automatic error fixing?')) {
            $this->info('Error fixing cancelled.');
            return;
        }

        $this->info('🔧 Attempting to fix errors...');

        $fixedCount = 0;
        $errorsByType = $this->detectedErrors->groupBy('type');

        foreach ($errorsByType as $type => $errors) {
            switch ($type) {
                case 'incorrect_ceiling_transitions':
                    $fixedCount += $this->fixIncorrectCeilingTransitions($errors);
                    break;
                case 'null_critical_fields':
                    $fixedCount += $this->fixNullCriticalFields($errors);
                    break;
                default:
                    $this->line("⚠️  No automatic fix available for: {$type}");
            }
        }

        $this->info("✅ Fixed {$fixedCount} errors automatically");
        $this->warn('⚠️  Please run the scan again to verify fixes and check for remaining errors');
    }

    /**
     * Fix incorrect ceiling transitions
     */
    private function fixIncorrectCeilingTransitions(Collection $errors): int
    {
        $fixedCount = 0;

        foreach ($errors as $error) {
            $data = $error['data'];
            $sale = Sale::find($data['sale_id']);

            if (!$sale) {
                continue;
            }

            try {
                // Fix sales with DISTRIBUTED status but no sale_ids
                if ($sale->ceiling === Ceiling::DISTRIBUTED->value && is_null($sale->sale_ids)) {
                    // Check if this sale has details, if so it might be a legitimate distributed sale
                    $detailsCount = SaleDetail::where('sale_id', $sale->id)->count();

                    if ($detailsCount === 0) {
                        // No details, likely should be ABOVE or BELOW
                        $sale->ceiling = Ceiling::ABOVE->value;
                        $sale->save();
                        $fixedCount++;
                        $this->line("  Fixed sale {$sale->id}: Changed from DISTRIBUTED to ABOVE (no details found)");
                    }
                }

                // Fix sales with sale_ids but not DISTRIBUTED status
                if ($sale->ceiling !== Ceiling::DISTRIBUTED->value && !is_null($sale->sale_ids)) {
                    $sale->sale_ids = null;
                    $sale->save();
                    $fixedCount++;
                    $this->line("  Fixed sale {$sale->id}: Cleared sale_ids (not DISTRIBUTED)");
                }

            } catch (\Exception $e) {
                $this->warn("  Failed to fix sale {$sale->id}: " . $e->getMessage());
            }
        }

        return $fixedCount;
    }

    /**
     * Fix null critical fields (where possible)
     */
    private function fixNullCriticalFields(Collection $errors): int
    {
        $fixedCount = 0;

        foreach ($errors as $error) {
            $data = $error['data'];
            $sale = Sale::find($data['sale_id']);

            if (!$sale) {
                continue;
            }

            try {
                $updated = false;

                // Set default values for null fields where appropriate
                if (is_null($sale->quantity)) {
                    $sale->quantity = 0;
                    $updated = true;
                }

                if (is_null($sale->value)) {
                    $sale->value = 0;
                    $updated = true;
                }

                if (is_null($sale->bonus)) {
                    $sale->bonus = 0;
                    $updated = true;
                }

                if ($updated) {
                    $sale->save();
                    $fixedCount++;
                    $this->line("  Fixed sale {$sale->id}: Set default values for null fields");
                }

            } catch (\Exception $e) {
                $this->warn("  Failed to fix sale {$sale->id}: " . $e->getMessage());
            }
        }

        return $fixedCount;
    }

    /**
     * Log summary of scan results
     */
    private function logSummary(): void
    {
        Log::info('Distribution error scan completed', [
            'date_range' => [
                'from' => $this->option('from-date'),
                'to' => $this->option('to-date')
            ],
            'distribution_type' => $this->option('distribution-type'),
            'total_errors' => $this->errorStats['total_errors'],
            'error_breakdown' => array_filter($this->errorStats, function($count) {
                return $count > 0;
            }),
            'options' => [
                'tolerance' => $this->tolerance,
                'detailed' => $this->detailed,
                'export_csv' => $this->exportCsv,
                'fix_errors' => $this->fixErrors
            ]
        ]);
    }
}
