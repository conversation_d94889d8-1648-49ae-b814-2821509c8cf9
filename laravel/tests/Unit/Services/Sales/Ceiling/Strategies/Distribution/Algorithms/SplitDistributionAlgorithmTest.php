<?php

namespace Tests\Unit\Services\Sales\Ceiling\Strategies\Distribution\Algorithms;

use App\Sale;
use App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms\SplitDistributionAlgorithm;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleDetailFactory;
use App\Services\SalesService;
use Tests\TestCase;
use Mockery;

/**
 * Test class for SplitDistributionAlgorithm
 *
 * Tests the split distribution algorithm that distributes excess sales using 90/10 split
 * (used by StoreStrategy)
 *
 * @covers \App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms\SplitDistributionAlgorithm
 */
class SplitDistributionAlgorithmTest extends TestCase
{
    private SplitDistributionAlgorithm $algorithm;
    private SalesService $salesService;
    private SaleDetailFactory $saleDetailFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $this->salesService = Mockery::mock(SalesService::class);
        $this->saleDetailFactory = Mockery::mock(SaleDetailFactory::class);

        $this->algorithm = new SplitDistributionAlgorithm(
            $this->salesService,
            $this->saleDetailFactory
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test successful distribution with default 90/10 split
     */
    public function test_distribute_excess_sale_success_with_default_split(): void
    {
        // Arrange
        $sale = $this->createMockSale();
        $originalSale = $this->createMockOriginalSale();
        $salesContributionBaseOn = [1, 2, 3];

        $primaryRatios = collect([
            (object) ['div_id' => 1, 'line_id' => 1, 'brick_id' => 1, 'percentage' => 0.6],
            (object) ['div_id' => 2, 'line_id' => 2, 'brick_id' => 2, 'percentage' => 0.4],
        ]);

        $secondaryRatios = collect([
            (object) ['div_id' => 3, 'line_id' => 3, 'brick_id' => 3, 'percentage' => 1.0],
        ]);

        $primaryDetails = [
            ['sale_id' => 1, 'div_id' => 1, 'quantity' => 54], // 60 * 0.9
            ['sale_id' => 1, 'div_id' => 2, 'quantity' => 36], // 40 * 0.9
        ];

        $secondaryDetails = [
            ['sale_id' => 1, 'div_id' => 3, 'quantity' => 10], // 100 * 0.1
        ];

        // Mock SalesService for primary distribution
        $this->salesService
            ->shouldReceive('getRatiosForDistribution')
            ->once()
            ->with($sale->date, $sale->product_id, $salesContributionBaseOn)
            ->andReturn($primaryRatios);

        // Mock SalesService for secondary distribution (empty array since details is null)
        $this->salesService
            ->shouldReceive('getRatiosForDistribution')
            ->once()
            ->with($sale->date, $sale->product_id, $salesContributionBaseOn, [])
            ->andReturn($secondaryRatios);

        // Mock SaleDetailFactory for primary details
        $this->saleDetailFactory
            ->shouldReceive('createDetailsFromRatios')
            ->once()
            ->with($sale, $primaryRatios->toArray(), 0.9, 0.9, 0.9, 0.9)
            ->andReturn($primaryDetails);

        // Mock SaleDetailFactory for secondary details
        $this->saleDetailFactory
            ->shouldReceive('createDetailsFromRatios')
            ->once()
            ->with($sale, $secondaryRatios->toArray(), 0.1, 0.1, 0.1, 0.1)
            ->andReturn($secondaryDetails);

        // Mock insertion
        $this->saleDetailFactory
            ->shouldReceive('insertDetails')
            ->once()
            ->with(array_merge($primaryDetails, $secondaryDetails))
            ->andReturn(true);

        // Act
        $result = $this->algorithm->distributeExcessSale($sale, $salesContributionBaseOn, $originalSale);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test distribution with custom percentages
     */
    public function test_distribute_excess_sale_with_custom_percentages(): void
    {
        // Arrange
        $customAlgorithm = new SplitDistributionAlgorithm(
            $this->salesService,
            $this->saleDetailFactory,
            0.8, // 80% primary
            0.2  // 20% secondary
        );

        $sale = $this->createMockSale();
        $originalSale = $this->createMockOriginalSale();
        $salesContributionBaseOn = [1, 2, 3];

        $primaryRatios = collect([
            (object) ['div_id' => 1, 'line_id' => 1, 'brick_id' => 1, 'percentage' => 1.0],
        ]);

        $secondaryRatios = collect([
            (object) ['div_id' => 2, 'line_id' => 2, 'brick_id' => 2, 'percentage' => 1.0],
        ]);

        $primaryDetails = [['sale_id' => 1, 'div_id' => 1, 'quantity' => 80]];
        $secondaryDetails = [['sale_id' => 1, 'div_id' => 2, 'quantity' => 20]];

        // Mock SalesService calls
        $this->salesService
            ->shouldReceive('getRatiosForDistribution')
            ->twice()
            ->andReturn($primaryRatios, $secondaryRatios);

        // Mock SaleDetailFactory calls
        $this->saleDetailFactory
            ->shouldReceive('createDetailsFromRatios')
            ->once()
            ->with($sale, $primaryRatios->toArray(), 0.8, 0.8, 0.8, 0.8)
            ->andReturn($primaryDetails);

        $this->saleDetailFactory
            ->shouldReceive('createDetailsFromRatios')
            ->once()
            ->with($sale, $secondaryRatios->toArray(), 0.2, 0.2, 0.2, 0.2)
            ->andReturn($secondaryDetails);

        $this->saleDetailFactory
            ->shouldReceive('insertDetails')
            ->once()
            ->andReturn(true);

        // Act
        $result = $customAlgorithm->distributeExcessSale($sale, $salesContributionBaseOn, $originalSale);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test distribution without original sale (fallback scenario)
     */
    public function test_distribute_excess_sale_without_original_sale(): void
    {
        // Arrange
        $sale = $this->createMockSale();
        $originalSale = null;
        $salesContributionBaseOn = [1, 2, 3];

        $primaryRatios = collect([
            (object) ['div_id' => 1, 'line_id' => 1, 'brick_id' => 1, 'percentage' => 1.0],
        ]);

        $primaryDetails = [['sale_id' => 1, 'div_id' => 1, 'quantity' => 90]];
        $secondaryDetails = [['sale_id' => 1, 'div_id' => 1, 'quantity' => 10]];

        // Mock SalesService - should be called twice with same parameters (fallback)
        $this->salesService
            ->shouldReceive('getRatiosForDistribution')
            ->twice()
            ->with($sale->date, $sale->product_id, $salesContributionBaseOn)
            ->andReturn($primaryRatios);

        // Mock SaleDetailFactory
        $this->saleDetailFactory
            ->shouldReceive('createDetailsFromRatios')
            ->twice()
            ->andReturn($primaryDetails, $secondaryDetails);

        $this->saleDetailFactory
            ->shouldReceive('insertDetails')
            ->once()
            ->andReturn(true);

        // Act
        $result = $this->algorithm->distributeExcessSale($sale, $salesContributionBaseOn, $originalSale);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test calculate excess quantity
     */
    public function test_calculate_excess_quantity(): void
    {
        // Arrange
        $ceilingSale = (object) [
            'number_of_units' => 150,
            'limit' => 100,
            'negative_limit' => -50
        ];

        // Act
        $result = $this->algorithm->calculateExcessQuantity($ceilingSale);

        // Assert
        $this->assertEquals(50, $result); // 150 - 100 = 50
    }

    /**
     * Test calculate excess quantity with null limits (LEFT JOIN scenario for STORES)
     */
    public function test_calculate_excess_quantity_with_null_limits(): void
    {
        // Arrange - positive units with null limit
        $ceilingSalePositive = (object) [
            'number_of_units' => 150,
            'limit' => null,
            'negative_limit' => -50
        ];

        // Arrange - negative units with null negative_limit
        $ceilingSaleNegative = (object) [
            'number_of_units' => -75,
            'limit' => 100,
            'negative_limit' => null
        ];

        // Act
        $resultPositive = $this->algorithm->calculateExcessQuantity($ceilingSalePositive);
        $resultNegative = $this->algorithm->calculateExcessQuantity($ceilingSaleNegative);

        // Assert
        $this->assertEquals(150, $resultPositive); // 150 - 0 = 150 (null limit defaults to 0)
        $this->assertEquals(-75, $resultNegative); // -75 - 0 = -75 (null negative_limit defaults to 0)
    }

    /**
     * Test distribution failure when insertion fails
     *
     * This test verifies that when details are created successfully but
     * the database insertion fails, the algorithm properly returns false.
     */
    public function test_distribute_excess_sale_insertion_failure(): void
    {
        // Arrange
        $sale = $this->createMockSale();
        $originalSale = $this->createMockOriginalSale();
        $salesContributionBaseOn = [1, 2, 3];

        $ratios = collect([
            (object) ['div_id' => 1, 'line_id' => 1, 'brick_id' => 1, 'percentage' => 1.0],
        ]);

        // Create proper detail structures with quantity fields for validation
        $primaryDetails = [
            ['sale_id' => 1, 'div_id' => 1, 'quantity' => 90, 'value' => 900]
        ];
        $secondaryDetails = [
            ['sale_id' => 1, 'div_id' => 1, 'quantity' => 10, 'value' => 100]
        ];

        // Mock SalesService
        $this->salesService
            ->shouldReceive('getRatiosForDistribution')
            ->twice()
            ->andReturn($ratios);

        // Mock SaleDetailFactory to create valid details but fail insertion
        $this->saleDetailFactory
            ->shouldReceive('createDetailsFromRatios')
            ->twice()
            ->andReturn($primaryDetails, $secondaryDetails);

        $this->saleDetailFactory
            ->shouldReceive('insertDetails')
            ->once()
            ->with(array_merge($primaryDetails, $secondaryDetails))
            ->andReturn(false); // Simulate insertion failure

        // Act
        $result = $this->algorithm->distributeExcessSale($sale, $salesContributionBaseOn, $originalSale);

        // Assert
        $this->assertFalse($result);
    }

    /**
     * Test that algorithm implements ExcessDistributorInterface
     */
    public function test_implements_excess_distributor_interface(): void
    {
        $this->assertInstanceOf(
            \App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\ExcessDistributorInterface::class,
            $this->algorithm
        );
    }

    /**
     * Create a mock Sale object for testing
     */
    private function createMockSale(): Sale
    {
        $sale = Mockery::mock(Sale::class);
        $sale->shouldReceive('setAttribute')->andReturnSelf();
        $sale->shouldReceive('getAttribute')->andReturnUsing(function ($key) {
            return match ($key) {
                'id' => 1,
                'date' => '2023-01-01',
                'product_id' => 1,
                'quantity' => 100,
                'value' => 1000,
                'bonus' => 50,
                default => null,
            };
        });

        $sale->id = 1;
        $sale->date = '2023-01-01';
        $sale->product_id = 1;
        $sale->quantity = 100;
        $sale->value = 1000;
        $sale->bonus = 50;

        return $sale;
    }

    /**
     * Create a mock original Sale with details for testing
     */
    private function createMockOriginalSale(): Sale
    {
        // Use partial mock to allow property access
        $sale = Mockery::mock(Sale::class)->makePartial();
        $sale->shouldReceive('setAttribute')->andReturnSelf();
        $sale->shouldReceive('getAttribute')->andReturnUsing(function ($key) {
            return match ($key) {
                'id' => 2,
                default => null,
            };
        });

        // Set properties directly - set details to null to trigger fallback
        $sale->id = 2;
        $sale->details = null;

        return $sale;
    }

}
